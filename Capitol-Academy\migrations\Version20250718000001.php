<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Rename course tables to onsite_course tables for Phase 1 restructuring
 */
final class Version20250718000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Rename course and course_module tables to onsite_course and onsite_course_module';
    }

    public function up(Schema $schema): void
    {
        // First, drop foreign key constraints that reference the course table
        $this->addSql('ALTER TABLE course_module DROP FOREIGN KEY FK_8F24F7A8591CC992');
        
        // Rename course table to onsite_course
        $this->addSql('RENAME TABLE course TO onsite_course');
        
        // Rename course_module table to onsite_course_module
        $this->addSql('RENAME TABLE course_module TO onsite_course_module');
        
        // Update the foreign key column name in onsite_course_module
        $this->addSql('ALTER TABLE onsite_course_module CHANGE course_id onsite_course_id INT NOT NULL');
        
        // Re-add the foreign key constraint with the new table and column names
        $this->addSql('ALTER TABLE onsite_course_module ADD CONSTRAINT FK_ONSITE_COURSE_MODULE_COURSE FOREIGN KEY (onsite_course_id) REFERENCES onsite_course (id) ON DELETE CASCADE');
        
        // Update any indexes that might reference the old column name
        $this->addSql('ALTER TABLE onsite_course_module ADD INDEX IDX_ONSITE_COURSE_MODULE_COURSE (onsite_course_id)');
    }

    public function down(Schema $schema): void
    {
        // Drop the new foreign key constraint
        $this->addSql('ALTER TABLE onsite_course_module DROP FOREIGN KEY FK_ONSITE_COURSE_MODULE_COURSE');
        $this->addSql('ALTER TABLE onsite_course_module DROP INDEX IDX_ONSITE_COURSE_MODULE_COURSE');
        
        // Rename back to original column name
        $this->addSql('ALTER TABLE onsite_course_module CHANGE onsite_course_id course_id INT NOT NULL');
        
        // Rename tables back to original names
        $this->addSql('RENAME TABLE onsite_course_module TO course_module');
        $this->addSql('RENAME TABLE onsite_course TO course');
        
        // Re-add the original foreign key constraint
        $this->addSql('ALTER TABLE course_module ADD CONSTRAINT FK_8F24F7A8591CC992 FOREIGN KEY (course_id) REFERENCES course (id) ON DELETE CASCADE');
    }
}
