<?php

namespace ContainerGHTN61x;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_8RUuq9sService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.8RUuq9s' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.8RUuq9s'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'entityManager' => ['services', 'doctrine.orm.default_entity_manager', 'getDoctrine_Orm_DefaultEntityManagerService', false],
            'instructor' => ['privates', '.errored..service_locator.8RUuq9s.App\\Entity\\Instructor', NULL, 'Cannot autowire service ".service_locator.8RUuq9s": it needs an instance of "App\\Entity\\Instructor" but this type has been excluded in "config/services.yaml".'],
        ], [
            'entityManager' => '?',
            'instructor' => 'App\\Entity\\Instructor',
        ]);
    }
}
