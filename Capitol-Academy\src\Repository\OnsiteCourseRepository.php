<?php

namespace App\Repository;

use App\Entity\OnsiteCourse;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OnsiteCourse>
 */
class OnsiteCourseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OnsiteCourse::class);
    }

    /**
     * Find all active onsite courses
     */
    public function findActiveCourses(): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('c.category', 'ASC')
            ->addOrderBy('c.code', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find onsite course by code
     */
    public function findByCode(string $code): ?OnsiteCourse
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.code = :code')
            ->andWhere('c.is_active = :active')
            ->setParameter('code', $code)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find onsite courses by category
     */
    public function findByCategory(string $category): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.category = :category')
            ->andWhere('c.is_active = :active')
            ->setParameter('category', $category)
            ->setParameter('active', true)
            ->orderBy('c.code', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find onsite courses by delivery mode
     */
    public function findByMode(string $mode): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.mode = :mode')
            ->andWhere('c.is_active = :active')
            ->setParameter('mode', $mode)
            ->setParameter('active', true)
            ->orderBy('c.created_at', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search onsite courses by title, description, or category for public search
     */
    public function searchByTitle(string $query, int $limit = 10): array
    {
        return $this->createQueryBuilder('c')
            ->where('c.is_active = :isActive')
            ->andWhere('c.title LIKE :query OR c.description LIKE :query OR c.category LIKE :query OR c.code LIKE :query')
            ->setParameter('isActive', true)
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('c.created_at', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total count of active onsite courses
     */
    public function getTotalActiveCount(): int
    {
        return $this->createQueryBuilder('c')
            ->select('COUNT(c.id)')
            ->where('c.is_active = :active')
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get onsite courses with pagination
     */
    public function findWithPagination(int $page = 1, int $limit = 10, ?string $search = null): array
    {
        $qb = $this->createQueryBuilder('c')
            ->orderBy('c.created_at', 'DESC');

        if ($search) {
            $qb->andWhere('c.title LIKE :search OR c.description LIKE :search OR c.code LIKE :search OR c.category LIKE :search')
               ->setParameter('search', '%' . $search . '%');
        }

        $qb->setFirstResult(($page - 1) * $limit)
           ->setMaxResults($limit);

        return $qb->getQuery()->getResult();
    }

    /**
     * Get total count for pagination
     */
    public function getTotalCount(?string $search = null): int
    {
        $qb = $this->createQueryBuilder('c')
            ->select('COUNT(c.id)');

        if ($search) {
            $qb->andWhere('c.title LIKE :search OR c.description LIKE :search OR c.code LIKE :search OR c.category LIKE :search')
               ->setParameter('search', '%' . $search . '%');
        }

        return $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * Find onsite courses by level
     */
    public function findByLevel(string $level): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.level = :level')
            ->andWhere('c.is_active = :active')
            ->setParameter('level', $level)
            ->setParameter('active', true)
            ->orderBy('c.code', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get popular onsite courses (by view count)
     */
    public function findPopularCourses(int $limit = 5): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('c.view_count', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get recent onsite courses
     */
    public function findRecentCourses(int $limit = 5): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('c.created_at', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get all unique categories for onsite courses
     */
    public function getCategories(): array
    {
        $result = $this->createQueryBuilder('c')
            ->select('DISTINCT c.category')
            ->where('c.category IS NOT NULL')
            ->andWhere('c.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('c.category', 'ASC')
            ->getQuery()
            ->getScalarResult();

        return array_column($result, 'category');
    }

    /**
     * Get all unique levels for onsite courses
     */
    public function getLevels(): array
    {
        $result = $this->createQueryBuilder('c')
            ->select('DISTINCT c.level')
            ->where('c.level IS NOT NULL')
            ->andWhere('c.is_active = :active')
            ->setParameter('active', true)
            ->orderBy('c.level', 'ASC')
            ->getQuery()
            ->getScalarResult();

        return array_column($result, 'level');
    }
}
