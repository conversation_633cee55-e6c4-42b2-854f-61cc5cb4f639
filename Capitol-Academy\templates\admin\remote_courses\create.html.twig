{% extends 'admin/base.html.twig' %}

{% block title %}Create Remote Course{% endblock %}

{% block page_title %}Create Remote Course{% endblock %}

{% block breadcrumb %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ path('admin_remote_course_index') }}">Remote Courses</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </ol>
    </nav>
{% endblock %}

{% block content %}
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-plus me-2"></i>Create New Remote Course
                        </h6>
                    </div>
                    <div class="card-body">
                        {{ form_start(form, {'attr': {'enctype': 'multipart/form-data'}}) }}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-code text-primary me-1"></i>{{ form_label(form.code) }}
                                    </label>
                                    {{ form_widget(form.code) }}
                                    {{ form_help(form.code) }}
                                    {{ form_errors(form.code) }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-tag text-primary me-1"></i>{{ form_label(form.category) }}
                                    </label>
                                    {{ form_widget(form.category) }}
                                    {{ form_errors(form.category) }}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-heading text-primary me-1"></i>{{ form_label(form.title) }}
                                </label>
                                {{ form_widget(form.title) }}
                                {{ form_errors(form.title) }}
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-align-left text-primary me-1"></i>{{ form_label(form.description) }}
                                </label>
                                {{ form_widget(form.description) }}
                                {{ form_errors(form.description) }}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-layer-group text-primary me-1"></i>{{ form_label(form.level) }}
                                    </label>
                                    {{ form_widget(form.level) }}
                                    {{ form_errors(form.level) }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-user-tie text-primary me-1"></i>{{ form_label(form.instructor) }}
                                    </label>
                                    {{ form_widget(form.instructor) }}
                                    {{ form_errors(form.instructor) }}
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-dollar-sign text-primary me-1"></i>{{ form_label(form.price) }}
                                    </label>
                                    {{ form_widget(form.price) }}
                                    {{ form_errors(form.price) }}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-image text-primary me-1"></i>{{ form_label(form.thumbnail_image) }}
                                    </label>
                                    {{ form_widget(form.thumbnail_image) }}
                                    {{ form_help(form.thumbnail_image) }}
                                    {{ form_errors(form.thumbnail_image) }}
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        <i class="fas fa-panorama text-primary me-1"></i>{{ form_label(form.banner_image) }}
                                    </label>
                                    {{ form_widget(form.banner_image) }}
                                    {{ form_help(form.banner_image) }}
                                    {{ form_errors(form.banner_image) }}
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-bullseye text-primary me-1"></i>{{ form_label(form.learning_outcomes) }}
                                </label>
                                <div id="learning-outcomes-collection" data-prototype="{{ form_widget(form.learning_outcomes.vars.prototype)|e('html_attr') }}">
                                    {% for outcome in form.learning_outcomes %}
                                        <div class="learning-outcome-item mb-2">
                                            <div class="input-group">
                                                {{ form_widget(outcome) }}
                                                <button type="button" class="btn btn-outline-danger remove-item">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm add-learning-outcome">
                                    <i class="fas fa-plus me-1"></i>Add Learning Outcome
                                </button>
                                {{ form_help(form.learning_outcomes) }}
                                {{ form_errors(form.learning_outcomes) }}
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-star text-primary me-1"></i>{{ form_label(form.features) }}
                                </label>
                                <div id="features-collection" data-prototype="{{ form_widget(form.features.vars.prototype)|e('html_attr') }}">
                                    {% for feature in form.features %}
                                        <div class="feature-item mb-2">
                                            <div class="input-group">
                                                {{ form_widget(feature) }}
                                                <button type="button" class="btn btn-outline-danger remove-item">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm add-feature">
                                    <i class="fas fa-plus me-1"></i>Add Feature
                                </button>
                                {{ form_help(form.features) }}
                                {{ form_errors(form.features) }}
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tags text-primary me-1"></i>{{ form_label(form.tags) }}
                                </label>
                                <div id="tags-collection" data-prototype="{{ form_widget(form.tags.vars.prototype)|e('html_attr') }}">
                                    {% for tag in form.tags %}
                                        <div class="tag-item mb-2">
                                            <div class="input-group">
                                                {{ form_widget(tag) }}
                                                <button type="button" class="btn btn-outline-danger remove-item">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm add-tag">
                                    <i class="fas fa-plus me-1"></i>Add Tag
                                </button>
                                {{ form_help(form.tags) }}
                                {{ form_errors(form.tags) }}
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-toggle-on text-primary me-1"></i>{{ form_label(form.is_active) }}
                                </label>
                                {{ form_widget(form.is_active) }}
                                {{ form_help(form.is_active) }}
                                {{ form_errors(form.is_active) }}
                            </div>
                        </div>

                        <div class="form-footer mt-4 pt-3 border-top">
                            <div class="d-flex justify-content-between">
                                <a href="{{ path('admin_remote_course_index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Cancel
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>Create Remote Course
                                </button>
                            </div>
                        </div>

                        {{ form_end(form) }}
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-info">
                            <i class="fas fa-info-circle me-2"></i>Course Creation Guide
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="text-primary">
                                <i class="fas fa-lightbulb me-1"></i>Tips for Success
                            </h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Use a unique, descriptive course code
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Write clear, compelling course titles
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Add detailed learning outcomes
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Upload high-quality images
                                </li>
                            </ul>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-primary">
                                <i class="fas fa-list-ol me-1"></i>Next Steps
                            </h6>
                            <ol class="list-unstyled">
                                <li class="mb-2">
                                    <span class="badge bg-primary me-2">1</span>
                                    Create the course
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-secondary me-2">2</span>
                                    Add chapters to organize content
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-secondary me-2">3</span>
                                    Assign videos to chapters
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-secondary me-2">4</span>
                                    Preview and publish
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // Collection management for dynamic form fields
        document.addEventListener('DOMContentLoaded', function() {
            setupCollectionManagement('learning-outcomes-collection', 'add-learning-outcome', 'learning-outcome-item');
            setupCollectionManagement('features-collection', 'add-feature', 'feature-item');
            setupCollectionManagement('tags-collection', 'add-tag', 'tag-item');
        });

        function setupCollectionManagement(collectionId, addButtonClass, itemClass) {
            const collection = document.getElementById(collectionId);
            const addButton = document.querySelector('.' + addButtonClass);
            
            if (!collection || !addButton) return;

            let index = collection.children.length;

            addButton.addEventListener('click', function() {
                const prototype = collection.dataset.prototype;
                const newForm = prototype.replace(/__name__/g, index);
                
                const wrapper = document.createElement('div');
                wrapper.className = itemClass + ' mb-2';
                wrapper.innerHTML = '<div class="input-group">' + newForm + 
                    '<button type="button" class="btn btn-outline-danger remove-item"><i class="fas fa-times"></i></button></div>';
                
                collection.appendChild(wrapper);
                index++;
            });

            collection.addEventListener('click', function(e) {
                if (e.target.closest('.remove-item')) {
                    e.target.closest('.' + itemClass).remove();
                }
            });
        }
    </script>
{% endblock %}
