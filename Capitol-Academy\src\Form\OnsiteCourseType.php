<?php

namespace App\Form;

use App\Entity\OnsiteCourse;
use App\Repository\CategoryRepository;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\GreaterThanOrEqual;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class OnsiteCourseType extends AbstractType
{
    public function __construct(
        private CategoryRepository $categoryRepository
    ) {
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Onsite Course Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter onsite course title'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Title is required']),
                    new Length(['max' => 255, 'maxMessage' => 'Title cannot be longer than 255 characters'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 4,
                    'placeholder' => 'Enter onsite course description'
                ],
                'constraints' => [
                    new Length(['max' => 2000, 'maxMessage' => 'Description cannot be longer than 2000 characters'])
                ]
            ])
            ->add('code', TextType::class, [
                'label' => 'Course Code',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., OSC001, TRAD101'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Course code is required']),
                    new Length(['max' => 10, 'maxMessage' => 'Course code cannot be longer than 10 characters'])
                ]
            ])
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => $this->getCategoryChoices(),
                'attr' => [
                    'class' => 'form-select'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Category is required'])
                ]
            ])
            ->add('level', ChoiceType::class, [
                'label' => 'Difficulty Level',
                'choices' => [
                    'Beginner' => 'Beginner',
                    'Intermediate' => 'Intermediate',
                    'Advanced' => 'Advanced'
                ],
                'required' => false,
                'attr' => [
                    'class' => 'form-select'
                ]
            ])
            ->add('duration', IntegerType::class, [
                'label' => 'Duration (minutes)',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter duration in minutes'
                ],
                'constraints' => [
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Duration must be greater than or equal to 0'])
                ]
            ])
            ->add('price', MoneyType::class, [
                'label' => 'Price',
                'currency' => 'USD',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '0.00'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Price is required']),
                    new GreaterThanOrEqual(['value' => 0, 'message' => 'Price must be greater than or equal to 0'])
                ]
            ])
            ->add('thumbnailFile', FileType::class, [
                'label' => 'Thumbnail Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, GIF, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 5MB.'
                    ])
                ],
                'help' => 'Upload a thumbnail image (300x200px recommended). Max size: 5MB'
            ])
            ->add('bannerFile', FileType::class, [
                'label' => 'Banner Image',
                'mapped' => false,
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '10M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, GIF, WebP)',
                        'maxSizeMessage' => 'The file is too large. Maximum size is 10MB.'
                    ])
                ],
                'help' => 'Upload a banner image (1200x400px recommended). Max size: 10MB'
            ])
            ->add('hasModules', CheckboxType::class, [
                'label' => 'Enable Modules',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Check this to enable modular structure for this onsite course'
            ])
            ->add('is_active', CheckboxType::class, [
                'label' => 'Active',
                'required' => false,
                'attr' => [
                    'class' => 'form-check-input'
                ],
                'help' => 'Check this to make the onsite course visible to students'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => OnsiteCourse::class,
        ]);
    }

    private function getCategoryChoices(): array
    {
        $categories = $this->categoryRepository->findForCourses();
        $choices = [];
        
        foreach ($categories as $category) {
            $choices[$category->getName()] = $category->getName();
        }
        
        return $choices;
    }
}
