<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerGHTN61x\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerGHTN61x/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerGHTN61x.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerGHTN61x\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerGHTN61x\App_KernelDevDebugContainer([
    'container.build_hash' => 'GHTN61x',
    'container.build_id' => 'a1459fb2',
    'container.build_time' => 1752886902,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerGHTN61x');
