<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerQL5xCZX\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerQL5xCZX/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerQL5xCZX.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerQL5xCZX\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerQL5xCZX\App_KernelDevDebugContainer([
    'container.build_hash' => 'QL5xCZX',
    'container.build_id' => 'fa4ed68f',
    'container.build_time' => 1752884427,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerQL5xCZX');
