{% extends 'admin/base.html.twig' %}

{% block title %}Remote Courses Management{% endblock %}

{% block page_title %}Remote Courses Management{% endblock %}

{% block breadcrumb %}
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Remote Courses</li>
        </ol>
    </nav>
{% endblock %}

{% block content %}
    <div class="container-fluid">
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Remote Courses</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-video fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Courses</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Enrollments</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_enrollments }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Revenue</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">${{ stats.total_revenue|number_format(2) }}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-video me-2"></i>Remote Courses
                </h6>
                <div class="d-flex align-items-center">
                    <!-- Search Form -->
                    <form method="GET" class="d-flex me-3">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control form-control-sm" 
                                   placeholder="Search courses..." value="{{ search }}" style="min-width: 200px;">
                            <button class="btn btn-outline-secondary btn-sm" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                            {% if search %}
                                <a href="{{ path('admin_remote_course_index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times"></i>
                                </a>
                            {% endif %}
                        </div>
                    </form>

                    <!-- Export Button -->
                    <button class="btn btn-success btn-sm me-2" onclick="exportTableToCSV('remote-courses.csv')">
                        <i class="fas fa-download me-1"></i>Export CSV
                    </button>

                    <!-- Add Button -->
                    <a href="{{ path('admin_remote_course_create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Add Remote Course
                    </a>
                </div>
            </div>

            <div class="card-body">
                {% if courses is empty %}
                    <div class="text-center py-5">
                        <i class="fas fa-video fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted mb-3">No remote courses found</h4>
                        {% if search %}
                            <p class="text-muted mb-4">No remote courses match your search criteria.</p>
                            <a href="{{ path('admin_remote_course_index') }}" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-1"></i>View All Courses
                            </a>
                        {% else %}
                            <p class="text-muted mb-4">Get started by creating your first remote course.</p>
                            <a href="{{ path('admin_remote_course_create') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>Create Remote Course
                            </a>
                        {% endif %}
                    </div>
                {% else %}
                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="dataTable">
                            <thead class="table-light">
                                <tr>
                                    {% for header in table_headers %}
                                        <th class="{% if header.sortable %}sortable{% endif %}">
                                            {{ header.label }}
                                            {% if header.sortable %}
                                                <i class="fas fa-sort text-muted ms-1"></i>
                                            {% endif %}
                                        </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in table_rows %}
                                    <tr>
                                        <td><strong>{{ row.data.code }}</strong></td>
                                        <td>
                                            <div class="fw-bold">{{ row.data.title }}</div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ row.data.category }}</span>
                                        </td>
                                        <td>{{ row.data.instructor }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ row.data.chapters }} chapters</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ row.data.videos }} videos</span>
                                        </td>
                                        <td><strong>{{ row.data.price }}</strong></td>
                                        <td>{{ row.data.status|raw }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ row.data.actions.edit }}" class="btn btn-sm btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ row.data.actions.chapters }}" class="btn btn-sm btn-outline-info" title="Manage Chapters">
                                                    <i class="fas fa-list"></i>
                                                </a>
                                                <a href="{{ row.data.actions.preview }}" class="btn btn-sm btn-outline-success" title="Preview">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="confirmDelete('{{ row.data.actions.delete }}', '{{ row.data.title }}')" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if pagination.total_pages > 1 %}
                        <nav aria-label="Remote courses pagination">
                            <ul class="pagination justify-content-center">
                                {% if pagination.current_page > 1 %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ path('admin_remote_course_index', {page: pagination.current_page - 1, search: search}) }}">Previous</a>
                                    </li>
                                {% endif %}

                                {% for page in 1..pagination.total_pages %}
                                    {% if page == pagination.current_page %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page }}</span>
                                        </li>
                                    {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ path('admin_remote_course_index', {page: page, search: search}) }}">{{ page }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if pagination.current_page < pagination.total_pages %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ path('admin_remote_course_index', {page: pagination.current_page + 1, search: search}) }}">Next</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the remote course "<span id="courseTitle"></span>"?</p>
                    <p class="text-danger"><strong>Warning:</strong> This action cannot be undone. All chapters and video assignments will be permanently deleted.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <input type="hidden" name="_token" value="{{ csrf_token('delete') }}">
                        <button type="submit" class="btn btn-danger">Delete Course</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        function confirmDelete(deleteUrl, courseTitle) {
            document.getElementById('courseTitle').textContent = courseTitle;
            document.getElementById('deleteForm').action = deleteUrl;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        function exportTableToCSV(filename) {
            // Implementation for CSV export
            const table = document.getElementById('dataTable');
            const csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');
                for (let j = 0; j < cols.length - 1; j++) { // Exclude actions column
                    row.push(cols[j].innerText);
                }
                csv.push(row.join(','));
            }
            
            const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = filename;
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
{% endblock %}
