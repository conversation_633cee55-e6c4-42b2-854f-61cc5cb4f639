<?php

namespace ContainerGHTN61x;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_LIsrtcxService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.LIsrtcx' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.LIsrtcx'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'onsiteCourseRepository' => ['privates', 'App\\Repository\\OnsiteCourseRepository', 'getOnsiteCourseRepositoryService', true],
        ], [
            'onsiteCourseRepository' => 'App\\Repository\\OnsiteCourseRepository',
        ]);
    }
}
