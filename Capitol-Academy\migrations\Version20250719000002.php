<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Create Remote Course System Tables
 */
final class Version20250719000002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create remote course system tables: remote_course, remote_course_chapter, remote_course_video';
    }

    public function up(Schema $schema): void
    {
        // Create remote_course table
        $this->addSql('CREATE TABLE remote_course (
            id INT AUTO_INCREMENT NOT NULL,
            code VARCHAR(10) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description LONGTEXT DEFAULT NULL,
            category VARCHAR(100) NOT NULL,
            level VARCHAR(50) DEFAULT NULL,
            price NUMERIC(10, 2) NOT NULL DEFAULT "0.00",
            thumbnail_image VARCHAR(255) DEFAULT NULL,
            banner_image VARCHAR(255) DEFAULT NULL,
            learning_outcomes JSON DEFAULT NULL,
            features <PERSON><PERSON><PERSON> DEFAULT NULL,
            view_count INT NOT NULL DEFAULT 0,
            enrolled_count INT NOT NULL DEFAULT 0,
            active_enrollments INT NOT NULL DEFAULT 0,
            completed_count INT NOT NULL DEFAULT 0,
            certified_count INT NOT NULL DEFAULT 0,
            average_rating NUMERIC(3, 2) NOT NULL DEFAULT "0.00",
            total_reviews INT NOT NULL DEFAULT 0,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            total_duration INT DEFAULT NULL,
            total_videos INT NOT NULL DEFAULT 0,
            instructor VARCHAR(255) DEFAULT NULL,
            tags JSON DEFAULT NULL,
            UNIQUE INDEX UNIQ_REMOTE_COURSE_CODE (code),
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create remote_course_chapter table
        $this->addSql('CREATE TABLE remote_course_chapter (
            id INT AUTO_INCREMENT NOT NULL,
            remote_course_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description LONGTEXT DEFAULT NULL,
            sort_order INT NOT NULL DEFAULT 0,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            updated_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            total_duration INT DEFAULT NULL,
            video_count INT NOT NULL DEFAULT 0,
            learning_objectives JSON DEFAULT NULL,
            INDEX IDX_REMOTE_COURSE_CHAPTER_COURSE (remote_course_id),
            CONSTRAINT FK_REMOTE_COURSE_CHAPTER_COURSE FOREIGN KEY (remote_course_id) REFERENCES remote_course (id) ON DELETE CASCADE,
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create remote_course_video table
        $this->addSql('CREATE TABLE remote_course_video (
            id INT AUTO_INCREMENT NOT NULL,
            chapter_id INT NOT NULL,
            video_id INT NOT NULL,
            sort_order INT NOT NULL DEFAULT 0,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            added_at DATETIME NOT NULL COMMENT "(DC2Type:datetime_immutable)",
            chapter_notes LONGTEXT DEFAULT NULL,
            chapter_specific_metadata JSON DEFAULT NULL,
            is_preview TINYINT(1) NOT NULL DEFAULT 0,
            is_required TINYINT(1) NOT NULL DEFAULT 1,
            INDEX IDX_REMOTE_COURSE_VIDEO_CHAPTER (chapter_id),
            INDEX IDX_REMOTE_COURSE_VIDEO_VIDEO (video_id),
            UNIQUE INDEX unique_chapter_video (chapter_id, video_id),
            CONSTRAINT FK_REMOTE_COURSE_VIDEO_CHAPTER FOREIGN KEY (chapter_id) REFERENCES remote_course_chapter (id) ON DELETE CASCADE,
            CONSTRAINT FK_REMOTE_COURSE_VIDEO_VIDEO FOREIGN KEY (video_id) REFERENCES video (id) ON DELETE CASCADE,
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }

    public function down(Schema $schema): void
    {
        // Drop tables in reverse order due to foreign key constraints
        $this->addSql('DROP TABLE remote_course_video');
        $this->addSql('DROP TABLE remote_course_chapter');
        $this->addSql('DROP TABLE remote_course');
    }
}
