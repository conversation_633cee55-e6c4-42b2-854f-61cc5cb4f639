<?php

namespace ContainerQL5xCZX;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getPaymentRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\PaymentRepository' shared autowired service.
     *
     * @return \App\Repository\PaymentRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'PaymentRepository.php';

        return $container->privates['App\\Repository\\PaymentRepository'] = new \App\Repository\PaymentRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
