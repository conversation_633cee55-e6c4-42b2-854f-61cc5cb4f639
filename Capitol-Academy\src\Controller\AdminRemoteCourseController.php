<?php

namespace App\Controller;

use App\Entity\Admin;
use App\Entity\RemoteCourse;
use App\Entity\RemoteCourseChapter;
use App\Entity\RemoteCourseVideo;
use App\Entity\Video;
use App\Form\RemoteCourseType;
use App\Form\RemoteCourseChapterType;
use App\Repository\RemoteCourseRepository;
use App\Repository\RemoteCourseChapterRepository;
use App\Repository\RemoteCourseVideoRepository;
use App\Repository\VideoRepository;
use App\Service\AdminPermissionService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/remote-courses')]
#[IsGranted('ROLE_ADMIN')]
class AdminRemoteCourseController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AdminPermissionService $permissionService,
        private RemoteCourseRepository $remoteCourseRepository,
        private RemoteCourseChapterRepository $chapterRepository,
        private RemoteCourseVideoRepository $courseVideoRepository,
        private VideoRepository $videoRepository
    ) {}

    #[Route('', name: 'admin_remote_course_index', methods: ['GET'])]
    public function index(Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.view')) {
            $this->permissionService->denyAccess('remote course management');
        }

        $search = $request->query->get('search', '');
        $page = $request->query->getInt('page', 1);
        $limit = 20;

        $courses = $this->remoteCourseRepository->findWithPagination($page, $limit, $search);
        $totalCourses = $this->remoteCourseRepository->getTotalCount($search);
        $stats = $this->remoteCourseRepository->getRemoteCourseStats();

        // Prepare table data
        $table_headers = [
            ['key' => 'code', 'label' => 'Code', 'sortable' => true],
            ['key' => 'title', 'label' => 'Title', 'sortable' => true],
            ['key' => 'category', 'label' => 'Category', 'sortable' => true],
            ['key' => 'instructor', 'label' => 'Instructor', 'sortable' => true],
            ['key' => 'chapters', 'label' => 'Chapters', 'sortable' => false],
            ['key' => 'videos', 'label' => 'Videos', 'sortable' => false],
            ['key' => 'price', 'label' => 'Price', 'sortable' => true],
            ['key' => 'status', 'label' => 'Status', 'sortable' => false],
            ['key' => 'actions', 'label' => 'Actions', 'sortable' => false]
        ];

        $table_rows = [];
        foreach ($courses as $course) {
            $table_rows[] = [
                'id' => $course->getId(),
                'data' => [
                    'code' => $course->getCode(),
                    'title' => $course->getTitle(),
                    'category' => $course->getCategory(),
                    'instructor' => $course->getInstructor() ?: 'Not assigned',
                    'chapters' => $course->getChapters()->count(),
                    'videos' => $course->getTotalVideos(),
                    'price' => $course->getFormattedPrice(),
                    'status' => $course->getStatusBadge(),
                    'actions' => [
                        'edit' => $this->generateUrl('admin_remote_course_edit', ['id' => $course->getId()]),
                        'chapters' => $this->generateUrl('admin_remote_course_chapters', ['id' => $course->getId()]),
                        'preview' => $this->generateUrl('admin_remote_course_preview', ['id' => $course->getId()]),
                        'delete' => $this->generateUrl('admin_remote_course_delete', ['id' => $course->getId()])
                    ]
                ]
            ];
        }

        return $this->render('admin/remote_courses/index.html.twig', [
            'admin' => $this->getUser(),
            'courses' => $courses,
            'stats' => $stats,
            'search' => $search,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($totalCourses / $limit),
                'total_items' => $totalCourses,
                'per_page' => $limit
            ],
            'table_headers' => $table_headers,
            'table_rows' => $table_rows
        ]);
    }

    #[Route('/create', name: 'admin_remote_course_create', methods: ['GET', 'POST'])]
    public function create(Request $request): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.create')) {
            $this->permissionService->denyAccess('remote course creation');
        }

        $course = new RemoteCourse();
        $form = $this->createForm(RemoteCourseType::class, $course);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->entityManager->persist($course);
                $this->entityManager->flush();

                $this->addFlash('success', 'Remote course created successfully!');
                return $this->redirectToRoute('admin_remote_course_edit', ['id' => $course->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating remote course: ' . $e->getMessage());
            }
        }

        return $this->render('admin/remote_courses/create.html.twig', [
            'admin' => $this->getUser(),
            'form' => $form->createView(),
            'course' => $course
        ]);
    }

    #[Route('/{id}/edit', name: 'admin_remote_course_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, RemoteCourse $course): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.edit')) {
            $this->permissionService->denyAccess('remote course editing');
        }

        $form = $this->createForm(RemoteCourseType::class, $course);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $course->setUpdatedAt(new \DateTimeImmutable());
                $this->entityManager->flush();

                $this->addFlash('success', 'Remote course updated successfully!');
                return $this->redirectToRoute('admin_remote_course_edit', ['id' => $course->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating remote course: ' . $e->getMessage());
            }
        }

        return $this->render('admin/remote_courses/edit.html.twig', [
            'admin' => $this->getUser(),
            'form' => $form->createView(),
            'course' => $course
        ]);
    }

    #[Route('/{id}/chapters', name: 'admin_remote_course_chapters', methods: ['GET'])]
    public function chapters(RemoteCourse $course): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.view')) {
            $this->permissionService->denyAccess('remote course management');
        }

        $chapters = $this->chapterRepository->findByCourseOrdered($course);
        $stats = $this->chapterRepository->getChapterStats($course);

        return $this->render('admin/remote_courses/chapters.html.twig', [
            'admin' => $this->getUser(),
            'course' => $course,
            'chapters' => $chapters,
            'stats' => $stats
        ]);
    }

    #[Route('/{id}/chapters/create', name: 'admin_remote_course_chapter_create', methods: ['GET', 'POST'])]
    public function createChapter(Request $request, RemoteCourse $course): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.edit')) {
            $this->permissionService->denyAccess('remote course editing');
        }

        $chapter = new RemoteCourseChapter();
        $chapter->setRemoteCourse($course);
        $chapter->setSortOrder($this->chapterRepository->getNextSortOrder($course));

        $form = $this->createForm(RemoteCourseChapterType::class, $chapter);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->entityManager->persist($chapter);
                $this->entityManager->flush();

                $this->addFlash('success', 'Chapter created successfully!');
                return $this->redirectToRoute('admin_remote_course_chapters', ['id' => $course->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating chapter: ' . $e->getMessage());
            }
        }

        return $this->render('admin/remote_courses/chapter_create.html.twig', [
            'admin' => $this->getUser(),
            'form' => $form->createView(),
            'course' => $course,
            'chapter' => $chapter
        ]);
    }

    #[Route('/chapters/{id}/edit', name: 'admin_remote_course_chapter_edit', methods: ['GET', 'POST'])]
    public function editChapter(Request $request, RemoteCourseChapter $chapter): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.edit')) {
            $this->permissionService->denyAccess('remote course editing');
        }

        $form = $this->createForm(RemoteCourseChapterType::class, $chapter);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $chapter->setUpdatedAt(new \DateTimeImmutable());
                $this->entityManager->flush();

                $this->addFlash('success', 'Chapter updated successfully!');
                return $this->redirectToRoute('admin_remote_course_chapters', ['id' => $chapter->getRemoteCourse()->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating chapter: ' . $e->getMessage());
            }
        }

        return $this->render('admin/remote_courses/chapter_edit.html.twig', [
            'admin' => $this->getUser(),
            'form' => $form->createView(),
            'chapter' => $chapter,
            'course' => $chapter->getRemoteCourse()
        ]);
    }

    #[Route('/chapters/{id}/videos', name: 'admin_remote_course_chapter_videos', methods: ['GET'])]
    public function chapterVideos(RemoteCourseChapter $chapter): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.view')) {
            $this->permissionService->denyAccess('remote course management');
        }

        $chapterVideos = $this->courseVideoRepository->findByChapterOrdered($chapter);
        $availableVideos = $this->videoRepository->findBy(['isActive' => true], ['title' => 'ASC']);
        $stats = $this->courseVideoRepository->getVideoStats($chapter);

        return $this->render('admin/remote_courses/chapter_videos.html.twig', [
            'admin' => $this->getUser(),
            'chapter' => $chapter,
            'course' => $chapter->getRemoteCourse(),
            'chapter_videos' => $chapterVideos,
            'available_videos' => $availableVideos,
            'stats' => $stats
        ]);
    }

    #[Route('/{id}/preview', name: 'admin_remote_course_preview', methods: ['GET'])]
    public function preview(RemoteCourse $course): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.view')) {
            $this->permissionService->denyAccess('remote course management');
        }

        $chapters = $this->chapterRepository->findWithVideos($course);

        return $this->render('admin/remote_courses/preview.html.twig', [
            'admin' => $this->getUser(),
            'course' => $course,
            'chapters' => $chapters
        ]);
    }

    #[Route('/{id}/delete', name: 'admin_remote_course_delete', methods: ['POST'])]
    public function delete(Request $request, RemoteCourse $course): Response
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.delete')) {
            $this->permissionService->denyAccess('remote course deletion');
        }

        if ($this->isCsrfTokenValid('delete' . $course->getId(), $request->request->get('_token'))) {
            try {
                $this->entityManager->remove($course);
                $this->entityManager->flush();

                $this->addFlash('success', 'Remote course deleted successfully!');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error deleting remote course: ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('admin_remote_course_index');
    }

    #[Route('/chapters/{id}/add-video', name: 'admin_remote_course_add_video', methods: ['POST'])]
    public function addVideoToChapter(Request $request, RemoteCourseChapter $chapter): JsonResponse
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.edit')) {
            return new JsonResponse(['success' => false, 'message' => 'Access denied'], 403);
        }

        $videoId = $request->request->getInt('video_id');
        $video = $this->videoRepository->find($videoId);

        if (!$video) {
            return new JsonResponse(['success' => false, 'message' => 'Video not found'], 404);
        }

        // Check if video is already in this chapter
        if ($this->courseVideoRepository->isVideoInChapter($video, $chapter)) {
            return new JsonResponse(['success' => false, 'message' => 'Video is already in this chapter'], 400);
        }

        try {
            $courseVideo = new RemoteCourseVideo();
            $courseVideo->setChapter($chapter);
            $courseVideo->setVideo($video);
            $courseVideo->setSortOrder($this->courseVideoRepository->getNextSortOrder($chapter));

            $this->entityManager->persist($courseVideo);
            $this->entityManager->flush();

            // Update chapter statistics
            $chapter->calculateTotalDuration();
            $chapter->calculateVideoCount();
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Video added to chapter successfully',
                'video_id' => $courseVideo->getId()
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'message' => 'Error adding video: ' . $e->getMessage()], 500);
        }
    }

    #[Route('/chapter-videos/{id}/remove', name: 'admin_remote_course_remove_video', methods: ['POST'])]
    public function removeVideoFromChapter(Request $request, RemoteCourseVideo $courseVideo): JsonResponse
    {
        // Check permissions
        if (!$this->permissionService->hasPermission('remote_courses.edit')) {
            return new JsonResponse(['success' => false, 'message' => 'Access denied'], 403);
        }

        if ($this->isCsrfTokenValid('remove_video' . $courseVideo->getId(), $request->request->get('_token'))) {
            try {
                $chapter = $courseVideo->getChapter();
                $this->entityManager->remove($courseVideo);
                $this->entityManager->flush();

                // Update chapter statistics
                $chapter->calculateTotalDuration();
                $chapter->calculateVideoCount();
                $this->entityManager->flush();

                return new JsonResponse(['success' => true, 'message' => 'Video removed from chapter successfully']);
            } catch (\Exception $e) {
                return new JsonResponse(['success' => false, 'message' => 'Error removing video: ' . $e->getMessage()], 500);
            }
        }

        return new JsonResponse(['success' => false, 'message' => 'Invalid CSRF token'], 400);
    }
}
