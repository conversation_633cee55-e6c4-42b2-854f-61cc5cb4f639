<?php

namespace App\Repository;

use App\Entity\OnsiteCourse;
use App\Entity\OnsiteCourseModule;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OnsiteCourseModule>
 */
class OnsiteCourseModuleRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OnsiteCourseModule::class);
    }

    /**
     * Find module by code
     */
    public function findByCode(string $code): ?OnsiteCourseModule
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.code = :code')
            ->andWhere('cm.is_active = :active')
            ->setParameter('code', $code)
            ->setParameter('active', true)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find modules by onsite course, ordered by sort_order
     */
    public function findByCourseOrdered($onsiteCourse): array
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->orderBy('cm.sort_order', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find all modules by onsite course (including inactive)
     */
    public function findAllByCourse(OnsiteCourse $onsiteCourse): array
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->orderBy('cm.sort_order', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get next sort order for an onsite course
     */
    public function getNextSortOrder(OnsiteCourse $onsiteCourse): int
    {
        $result = $this->createQueryBuilder('cm')
            ->select('MAX(cm.sort_order)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->getQuery()
            ->getSingleScalarResult();

        return ($result ?? 0) + 1;
    }

    /**
     * Get module statistics for an onsite course
     */
    public function getModuleStats(OnsiteCourse $onsiteCourse): array
    {
        $qb = $this->createQueryBuilder('cm');

        $totalModules = $qb->select('COUNT(cm.id)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->getQuery()
            ->getSingleScalarResult();

        $activeModules = $qb->select('COUNT(cm.id)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        $totalDuration = $qb->select('SUM(cm.duration)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        $totalPrice = $qb->select('SUM(cm.price)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total' => $totalModules,
            'active' => $activeModules,
            'total_duration' => $totalDuration ?? 0,
            'total_price' => $totalPrice ?? 0
        ];
    }

    /**
     * Find modules by display order
     */
    public function findByDisplayOrder(OnsiteCourse $onsiteCourse): array
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->orderBy('cm.displayOrder', 'ASC')
            ->addOrderBy('cm.sort_order', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get active modules count for an onsite course
     */
    public function getActiveModulesCount(OnsiteCourse $onsiteCourse): int
    {
        return $this->createQueryBuilder('cm')
            ->select('COUNT(cm.id)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Find modules with content (description, outcomes, or features)
     */
    public function findModulesWithContent(OnsiteCourse $onsiteCourse): array
    {
        return $this->createQueryBuilder('cm')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->andWhere('cm.description IS NOT NULL OR cm.learning_outcomes IS NOT NULL OR cm.features IS NOT NULL')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->orderBy('cm.sort_order', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total duration for all active modules in an onsite course
     */
    public function getTotalDuration(OnsiteCourse $onsiteCourse): int
    {
        $result = $this->createQueryBuilder('cm')
            ->select('SUM(cm.duration)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->andWhere('cm.duration IS NOT NULL')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        return $result ?? 0;
    }

    /**
     * Get total price for all active modules in an onsite course
     */
    public function getTotalPrice(OnsiteCourse $onsiteCourse): float
    {
        $result = $this->createQueryBuilder('cm')
            ->select('SUM(cm.price)')
            ->andWhere('cm.onsiteCourse = :onsiteCourse')
            ->andWhere('cm.is_active = :active')
            ->andWhere('cm.price IS NOT NULL')
            ->setParameter('onsiteCourse', $onsiteCourse)
            ->setParameter('active', true)
            ->getQuery()
            ->getSingleScalarResult();

        return (float)($result ?? 0);
    }
}
