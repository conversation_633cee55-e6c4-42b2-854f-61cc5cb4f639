<?php

namespace App\Controller;

use App\Entity\OnsiteCourse;
use App\Repository\OnsiteCourseRepository;
use App\Repository\OnsiteCourseModuleRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/onsite-courses')]
class OnsiteCourseController extends AbstractController
{
    #[Route('', name: 'app_onsite_course_index')]
    public function index(OnsiteCourseRepository $onsiteCourseRepository): Response
    {
        $courses = $onsiteCourseRepository->findActiveCourses();

        return $this->render('onsite_course/index.html.twig', [
            'courses' => $courses,
        ]);
    }

    #[Route('/{code}', name: 'app_onsite_course_show')]
    public function show(string $code, OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        $course = $onsiteCourseRepository->findByCode($code);

        if (!$course) {
            throw $this->createNotFoundException('Onsite course not found');
        }

        // Increment view count
        $course->incrementViewCount();
        $entityManager->flush();

        $modules = $moduleRepository->findByCourseOrdered($course);

        // Since we simplified the course system, no enrollment check needed
        $isEnrolled = false;
        $user = $this->getUser();

        return $this->render('onsite_course/show.html.twig', [
            'course' => $course,
            'modules' => $modules,
            'is_enrolled' => $isEnrolled,
            'user' => $user,
        ]);
    }

    #[Route('/category/{category}', name: 'app_onsite_course_category')]
    public function category(string $category, OnsiteCourseRepository $onsiteCourseRepository): Response
    {
        $courses = $onsiteCourseRepository->findByCategory($category);

        return $this->render('onsite_course/category.html.twig', [
            'courses' => $courses,
            'category' => $category,
        ]);
    }

    #[Route('/level/{level}', name: 'app_onsite_course_level')]
    public function level(string $level, OnsiteCourseRepository $onsiteCourseRepository): Response
    {
        $courses = $onsiteCourseRepository->findByLevel($level);

        return $this->render('onsite_course/level.html.twig', [
            'courses' => $courses,
            'level' => $level,
        ]);
    }

    // Individual onsite course routes for legacy compatibility
    #[Route('/financial-markets', name: 'app_onsite_course_fma')]
    public function financialMarkets(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('FMA', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/technical-analysis', name: 'app_onsite_course_tec')]
    public function technicalAnalysis(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('TEC', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/trading-strategies', name: 'app_onsite_course_trs')]
    public function tradingStrategies(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('TRS', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/risk-management', name: 'app_onsite_course_rm')]
    public function riskManagement(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('RM', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/psychology-trading', name: 'app_onsite_course_pt')]
    public function psychologyTrading(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('PT', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/portfolio-management', name: 'app_onsite_course_pm')]
    public function portfolioManagement(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('PM', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/algorithmic-trading', name: 'app_onsite_course_at')]
    public function algorithmicTrading(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('AT', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/cryptocurrency-trading', name: 'app_onsite_course_ct')]
    public function cryptocurrencyTrading(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('CT', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/forex-trading', name: 'app_onsite_course_ft')]
    public function forexTrading(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('FT', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/options-trading', name: 'app_onsite_course_ot')]
    public function optionsTrading(OnsiteCourseRepository $onsiteCourseRepository, OnsiteCourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('OT', $onsiteCourseRepository, $moduleRepository, $entityManager);
    }
}
