<?php

namespace ContainerGHTN61x;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_Errored_GsH2V8rService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored.gsH2V8r' shared service.
     *
     * @return \App\Repository\CourseRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot determine controller argument for "App\\Controller\\Admin\\DashboardController::index()": the $courseRepository argument is type-hinted with the non-existent class or interface: "App\\Repository\\CourseRepository".');
    }
}
