<?php

namespace App\Form;

use App\Entity\RemoteCourseChapter;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\PositiveOrZero;

class RemoteCourseChapterType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', TextType::class, [
                'label' => 'Chapter Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter chapter title',
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Chapter title is required'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Chapter Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 4,
                    'placeholder' => 'Describe what this chapter covers'
                ],
                'help' => 'Optional description of the chapter content and objectives'
            ])
            ->add('sort_order', NumberType::class, [
                'label' => 'Sort Order',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '1',
                    'min' => 0
                ],
                'constraints' => [
                    new PositiveOrZero(['message' => 'Sort order must be zero or positive'])
                ],
                'help' => 'Determines the order of chapters in the course (lower numbers appear first)'
            ])
            ->add('learning_objectives', CollectionType::class, [
                'label' => 'Learning Objectives',
                'entry_type' => TextType::class,
                'entry_options' => [
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'What will students learn in this chapter?'
                    ]
                ],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
                'attr' => [
                    'class' => 'learning-objectives-collection'
                ],
                'help' => 'Add specific learning objectives for this chapter'
            ])
            ->add('is_active', ChoiceType::class, [
                'label' => 'Status',
                'choices' => [
                    'Active' => true,
                    'Inactive' => false
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'help' => 'Only active chapters are visible to students'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => RemoteCourseChapter::class,
            'attr' => [
                'class' => 'remote-course-chapter-form',
                'novalidate' => 'novalidate'
            ]
        ]);
    }
}
