<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/market_analysis/index.html.twig */
class __TwigTemplate_b367dbd4873c60066507c10a3a656efe extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Market Analysis Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Market Analysis Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Market Analysis</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Market Analysis Management", "page_icon" => "fas fa-chart-bar", "search_placeholder" => "Search articles by title, author, or asset type...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_create"), "text" => "Add New Article", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Articles", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 25
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 25, $this->source); })()), "total", [], "any", false, false, false, 25), "icon" => "fas fa-newspaper", "color" => "#1e3c72", "gradient" => "linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)"], ["title" => "Active", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 32
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 32, $this->source); })()), "published", [], "any", false, false, false, 32), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Inactive", "value" => CoreExtension::getAttribute($this->env, $this->source,         // line 39
(isset($context["stats"]) || array_key_exists("stats", $context) ? $context["stats"] : (function () { throw new RuntimeError('Variable "stats" does not exist.', 39, $this->source); })()), "draft", [], "any", false, false, false, 39), "icon" => "fas fa-pause-circle", "color" => "#6c757d", "gradient" => "linear-gradient(135deg, #6c757d 0%, #495057 100%)"]]];
        // line 46
        yield "
";
        // line 47
        yield from $this->load("admin/market_analysis/index.html.twig", 47, "792112813")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 47, $this->source); })())));
        // line 116
        yield "




<!-- Include Modals -->
";
        // line 122
        yield from $this->load("admin/market_analysis/_modals.html.twig", 122)->unwrap()->yield($context);
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 125
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 126
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.article-row',
        ['.article-title', '.article-author', '.article-asset-type']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// CSRF Token generation function
function generateCsrfToken(intention) {
    // For simplicity, we'll use a synchronous approach
    // In a real application, you might want to fetch this from the server
    const tokens = {
        ";
        // line 147
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["articles"]) || array_key_exists("articles", $context) ? $context["articles"] : (function () { throw new RuntimeError('Variable "articles" does not exist.', 147, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["article"]) {
            // line 148
            yield "        'toggle";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "id", [], "any", false, false, false, 148), "html", null, true);
            yield "': '";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken(("toggle" . CoreExtension::getAttribute($this->env, $this->source, $context["article"], "id", [], "any", false, false, false, 148))), "html", null, true);
            yield "',

        'delete";
            // line 150
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["article"], "id", [], "any", false, false, false, 150), "html", null, true);
            yield "': '";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->env->getRuntime('Symfony\Component\Form\FormRenderer')->renderCsrfToken(("delete" . CoreExtension::getAttribute($this->env, $this->source, $context["article"], "id", [], "any", false, false, false, 150))), "html", null, true);
            yield "',
        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['article'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 152
        yield "    };
    return tokens[intention] || '';
}

// Article management functions using standardized modals
function toggleArticleStatus(articleId, articleTitle, currentStatus) {
    showStatusModal(articleTitle, currentStatus, function() {
        executeStatusToggle(articleId);
    });
}

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}



function deleteArticle(articleId, articleTitle) {
    showDeleteModal(articleTitle, function() {
        executeArticleDelete(articleId);
    });
}

function executeStatusToggle(articleId) {
    // Generate CSRF token for this specific article
    fetch(`";
        // line 189
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_toggle_status", ["id" => "__ID__"]);
        yield "`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('toggle' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while updating the article status.');
    });
}



function executeArticleDelete(articleId) {
    fetch(`";
        // line 217
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_delete", ["id" => "__ID__"]);
        yield "`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('delete' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while deleting the article.');
    });
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Toast notification functions - REMOVED
/*
function showSuccessToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class=\"fas fa-check-circle me-2\"></i>\${message}
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
*/

function showErrorToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class=\"fas fa-exclamation-circle me-2\"></i>\${message}
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/market_analysis/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  311 => 217,  280 => 189,  241 => 152,  231 => 150,  223 => 148,  219 => 147,  196 => 126,  183 => 125,  172 => 122,  164 => 116,  162 => 47,  159 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Market Analysis Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Market Analysis Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Market Analysis</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Market Analysis Management',
    'page_icon': 'fas fa-chart-bar',
    'search_placeholder': 'Search articles by title, author, or asset type...',
    'create_button': {
        'url': path('admin_market_analysis_create'),
        'text': 'Add New Article',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Articles',
            'value': stats.total,
            'icon': 'fas fa-newspaper',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Active',
            'value': stats.published,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': stats.draft,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Title'},
            {'text': 'Asset Type'},
            {'text': 'Author'},
            {'text': 'Publish Date'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for article in articles %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center\">
                        <img src=\"' ~ article.thumbnailUrl ~ '\" alt=\"Thumbnail\" class=\"rounded\" style=\"width: 50px; height: 40px; object-fit: cover;\">
                    </div>'
                },
                {
                    'content': '<div class=\"article-title\">
                        <strong>' ~ article.title ~ '</strong>
                        <br><small class=\"text-muted\">' ~ article.shortExcerpt(60) ~ '</small>
                    </div>',
                    'class': 'article-title'
                },
                {
                    'content': '<span class=\"badge\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px;\">' ~ article.assetTypeLabel ~ '</span>',
                    'class': 'article-asset-type'
                },
                {
                    'content': article.author ? '<span class=\"article-author\">' ~ article.author ~ '</span>' : '<span class=\"text-muted\">No author</span>',
                    'class': 'article-author'
                },
                {
                    'content': '<span class=\"text-muted\">' ~ article.formattedPublishDate ~ '</span>'
                },
                {
                    'content': '<span class=\"badge ' ~ article.statusBadgeClass ~ ' px-3 py-2\">' ~ article.statusLabel ~ '</span>'
                },

                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_market_analysis_show_readable', {'slug': article.slug}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_market_analysis_edit_readable', {'slug': article.slug}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Article\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Toggle Status\" onclick=\"toggleArticleStatus(' ~ article.id ~ ', \\'' ~ article.title ~ '\\', ' ~ article.isActive ~ ')\"><i class=\"fas fa-toggle-on\"></i></button>

                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Article\" onclick=\"deleteArticle(' ~ article.id ~ ', \\'' ~ article.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'article-row',
            'empty_message': 'No market analysis articles found',
            'empty_icon': 'fas fa-chart-bar',
            'empty_description': 'Get started by adding your first market analysis article.',
            'search_config': {
                'fields': ['.article-title', '.article-author', '.article-asset-type']
            }
        } %}
    {% endblock %}
{% endembed %}





<!-- Include Modals -->
{% include 'admin/market_analysis/_modals.html.twig' %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.article-row',
        ['.article-title', '.article-author', '.article-asset-type']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// CSRF Token generation function
function generateCsrfToken(intention) {
    // For simplicity, we'll use a synchronous approach
    // In a real application, you might want to fetch this from the server
    const tokens = {
        {% for article in articles %}
        'toggle{{ article.id }}': '{{ csrf_token('toggle' ~ article.id) }}',

        'delete{{ article.id }}': '{{ csrf_token('delete' ~ article.id) }}',
        {% endfor %}
    };
    return tokens[intention] || '';
}

// Article management functions using standardized modals
function toggleArticleStatus(articleId, articleTitle, currentStatus) {
    showStatusModal(articleTitle, currentStatus, function() {
        executeStatusToggle(articleId);
    });
}

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}



function deleteArticle(articleId, articleTitle) {
    showDeleteModal(articleTitle, function() {
        executeArticleDelete(articleId);
    });
}

function executeStatusToggle(articleId) {
    // Generate CSRF token for this specific article
    fetch(`{{ path('admin_market_analysis_toggle_status', {'id': '__ID__'}) }}`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('toggle' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while updating the article status.');
    });
}



function executeArticleDelete(articleId) {
    fetch(`{{ path('admin_market_analysis_delete', {'id': '__ID__'}) }}`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('delete' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while deleting the article.');
    });
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Toast notification functions - REMOVED
/*
function showSuccessToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class=\"fas fa-check-circle me-2\"></i>\${message}
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
*/

function showErrorToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class=\"fas fa-exclamation-circle me-2\"></i>\${message}
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
", "admin/market_analysis/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\market_analysis\\index.html.twig");
    }
}


/* admin/market_analysis/index.html.twig */
class __TwigTemplate_b367dbd4873c60066507c10a3a656efe___792112813 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 47
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/market_analysis/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 47);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 48
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 49
        yield "        <!-- Standardized Table -->
        ";
        // line 50
        $context["table_headers"] = [["text" => "Thumbnail"], ["text" => "Title"], ["text" => "Asset Type"], ["text" => "Author"], ["text" => "Publish Date"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 59
        yield "
        ";
        // line 60
        $context["table_rows"] = [];
        // line 61
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["articles"]) || array_key_exists("articles", $context) ? $context["articles"] : (function () { throw new RuntimeError('Variable "articles" does not exist.', 61, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["article"]) {
            // line 62
            yield "            ";
            $context["row_cells"] = [["content" => (("<div class=\"d-flex align-items-center\">
                        <img src=\"" . CoreExtension::getAttribute($this->env, $this->source,             // line 65
$context["article"], "thumbnailUrl", [], "any", false, false, false, 65)) . "\" alt=\"Thumbnail\" class=\"rounded\" style=\"width: 50px; height: 40px; object-fit: cover;\">
                    </div>")], ["content" => (((("<div class=\"article-title\">
                        <strong>" . CoreExtension::getAttribute($this->env, $this->source,             // line 70
$context["article"], "title", [], "any", false, false, false, 70)) . "</strong>
                        <br><small class=\"text-muted\">") . CoreExtension::getAttribute($this->env, $this->source,             // line 71
$context["article"], "shortExcerpt", [60], "method", false, false, false, 71)) . "</small>
                    </div>"), "class" => "article-title"], ["content" => (("<span class=\"badge\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px;\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 76
$context["article"], "assetTypeLabel", [], "any", false, false, false, 76)) . "</span>"), "class" => "article-asset-type"], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 80
$context["article"], "author", [], "any", false, false, false, 80)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<span class=\"article-author\">" . CoreExtension::getAttribute($this->env, $this->source, $context["article"], "author", [], "any", false, false, false, 80)) . "</span>")) : ("<span class=\"text-muted\">No author</span>")), "class" => "article-author"], ["content" => (("<span class=\"text-muted\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 84
$context["article"], "formattedPublishDate", [], "any", false, false, false, 84)) . "</span>")], ["content" => (((("<span class=\"badge " . CoreExtension::getAttribute($this->env, $this->source,             // line 87
$context["article"], "statusBadgeClass", [], "any", false, false, false, 87)) . " px-3 py-2\">") . CoreExtension::getAttribute($this->env, $this->source, $context["article"], "statusLabel", [], "any", false, false, false, 87)) . "</span>")], ["content" => (((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_show_readable", ["slug" => CoreExtension::getAttribute($this->env, $this->source,             // line 92
$context["article"], "slug", [], "any", false, false, false, 92)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_market_analysis_edit_readable", ["slug" => CoreExtension::getAttribute($this->env, $this->source,             // line 93
$context["article"], "slug", [], "any", false, false, false, 93)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Article\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Toggle Status\" onclick=\"toggleArticleStatus(") . CoreExtension::getAttribute($this->env, $this->source,             // line 94
$context["article"], "id", [], "any", false, false, false, 94)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["article"], "title", [], "any", false, false, false, 94)) . "', ") . CoreExtension::getAttribute($this->env, $this->source, $context["article"], "isActive", [], "any", false, false, false, 94)) . ")\"><i class=\"fas fa-toggle-on\"></i></button>

                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Article\" onclick=\"deleteArticle(") . CoreExtension::getAttribute($this->env, $this->source,             // line 96
$context["article"], "id", [], "any", false, false, false, 96)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["article"], "title", [], "any", false, false, false, 96)) . "')\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 100
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 100, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 100, $this->source); })())]]);
            // line 101
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['article'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 102
        yield "
        ";
        // line 103
        yield from $this->load("components/admin_table.html.twig", 103)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 104
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 104, $this->source); })()), "rows" =>         // line 105
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 105, $this->source); })()), "row_class" => "article-row", "empty_message" => "No market analysis articles found", "empty_icon" => "fas fa-chart-bar", "empty_description" => "Get started by adding your first market analysis article.", "search_config" => ["fields" => [".article-title", ".article-author", ".article-asset-type"]]]));
        // line 114
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/market_analysis/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  843 => 114,  841 => 105,  840 => 104,  839 => 103,  836 => 102,  830 => 101,  827 => 100,  824 => 96,  821 => 94,  819 => 93,  817 => 92,  815 => 87,  814 => 84,  813 => 80,  812 => 76,  810 => 71,  808 => 70,  805 => 65,  802 => 62,  797 => 61,  795 => 60,  792 => 59,  790 => 50,  787 => 49,  774 => 48,  751 => 47,  311 => 217,  280 => 189,  241 => 152,  231 => 150,  223 => 148,  219 => 147,  196 => 126,  183 => 125,  172 => 122,  164 => 116,  162 => 47,  159 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Market Analysis Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Market Analysis Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Market Analysis</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Market Analysis Management',
    'page_icon': 'fas fa-chart-bar',
    'search_placeholder': 'Search articles by title, author, or asset type...',
    'create_button': {
        'url': path('admin_market_analysis_create'),
        'text': 'Add New Article',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Articles',
            'value': stats.total,
            'icon': 'fas fa-newspaper',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Active',
            'value': stats.published,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Inactive',
            'value': stats.draft,
            'icon': 'fas fa-pause-circle',
            'color': '#6c757d',
            'gradient': 'linear-gradient(135deg, #6c757d 0%, #495057 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Title'},
            {'text': 'Asset Type'},
            {'text': 'Author'},
            {'text': 'Publish Date'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for article in articles %}
            {% set row_cells = [
                {
                    'content': '<div class=\"d-flex align-items-center\">
                        <img src=\"' ~ article.thumbnailUrl ~ '\" alt=\"Thumbnail\" class=\"rounded\" style=\"width: 50px; height: 40px; object-fit: cover;\">
                    </div>'
                },
                {
                    'content': '<div class=\"article-title\">
                        <strong>' ~ article.title ~ '</strong>
                        <br><small class=\"text-muted\">' ~ article.shortExcerpt(60) ~ '</small>
                    </div>',
                    'class': 'article-title'
                },
                {
                    'content': '<span class=\"badge\" style=\"background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 0.5rem 0.75rem; border-radius: 6px;\">' ~ article.assetTypeLabel ~ '</span>',
                    'class': 'article-asset-type'
                },
                {
                    'content': article.author ? '<span class=\"article-author\">' ~ article.author ~ '</span>' : '<span class=\"text-muted\">No author</span>',
                    'class': 'article-author'
                },
                {
                    'content': '<span class=\"text-muted\">' ~ article.formattedPublishDate ~ '</span>'
                },
                {
                    'content': '<span class=\"badge ' ~ article.statusBadgeClass ~ ' px-3 py-2\">' ~ article.statusLabel ~ '</span>'
                },

                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_market_analysis_show_readable', {'slug': article.slug}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Details\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_market_analysis_edit_readable', {'slug': article.slug}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Article\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Toggle Status\" onclick=\"toggleArticleStatus(' ~ article.id ~ ', \\'' ~ article.title ~ '\\', ' ~ article.isActive ~ ')\"><i class=\"fas fa-toggle-on\"></i></button>

                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Article\" onclick=\"deleteArticle(' ~ article.id ~ ', \\'' ~ article.title ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'article-row',
            'empty_message': 'No market analysis articles found',
            'empty_icon': 'fas fa-chart-bar',
            'empty_description': 'Get started by adding your first market analysis article.',
            'search_config': {
                'fields': ['.article-title', '.article-author', '.article-asset-type']
            }
        } %}
    {% endblock %}
{% endembed %}





<!-- Include Modals -->
{% include 'admin/market_analysis/_modals.html.twig' %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.article-row',
        ['.article-title', '.article-author', '.article-asset-type']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// CSRF Token generation function
function generateCsrfToken(intention) {
    // For simplicity, we'll use a synchronous approach
    // In a real application, you might want to fetch this from the server
    const tokens = {
        {% for article in articles %}
        'toggle{{ article.id }}': '{{ csrf_token('toggle' ~ article.id) }}',

        'delete{{ article.id }}': '{{ csrf_token('delete' ~ article.id) }}',
        {% endfor %}
    };
    return tokens[intention] || '';
}

// Article management functions using standardized modals
function toggleArticleStatus(articleId, articleTitle, currentStatus) {
    showStatusModal(articleTitle, currentStatus, function() {
        executeStatusToggle(articleId);
    });
}

// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}



function deleteArticle(articleId, articleTitle) {
    showDeleteModal(articleTitle, function() {
        executeArticleDelete(articleId);
    });
}

function executeStatusToggle(articleId) {
    // Generate CSRF token for this specific article
    fetch(`{{ path('admin_market_analysis_toggle_status', {'id': '__ID__'}) }}`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('toggle' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while updating the article status.');
    });
}



function executeArticleDelete(articleId) {
    fetch(`{{ path('admin_market_analysis_delete', {'id': '__ID__'}) }}`.replace('__ID__', articleId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_token': generateCsrfToken('delete' + articleId)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the success message - just reload the page
            setTimeout(() => location.reload(), 500);
        } else {
            showErrorToast(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showErrorToast('An error occurred while deleting the article.');
    });
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;
    
    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };
    
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Toast notification functions - REMOVED
/*
function showSuccessToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class=\"fas fa-check-circle me-2\"></i>\${message}
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
*/

function showErrorToast(message) {
    // Create a simple alert for now - can be replaced with a proper toast system
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class=\"fas fa-exclamation-circle me-2\"></i>\${message}
        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
", "admin/market_analysis/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\market_analysis\\index.html.twig");
    }
}
