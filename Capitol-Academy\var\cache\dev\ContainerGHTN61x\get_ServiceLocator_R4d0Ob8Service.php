<?php

namespace ContainerGHTN61x;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_R4d0Ob8Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.r4d0Ob8' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.r4d0Ob8'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', '.errored.mrPcMDJ', NULL, 'Cannot determine controller argument for "App\\Controller\\HomeController::index()": the $courseRepository argument is type-hinted with the non-existent class or interface: "App\\Repository\\CourseRepository".'],
            'partnerRepository' => ['privates', 'App\\Repository\\PartnerRepository', 'getPartnerRepositoryService', false],
            'videoRepository' => ['privates', 'App\\Repository\\VideoRepository', 'getVideoRepositoryService', true],
        ], [
            'courseRepository' => '?',
            'partnerRepository' => 'App\\Repository\\PartnerRepository',
            'videoRepository' => 'App\\Repository\\VideoRepository',
        ]);
    }
}
