<?php

namespace ContainerQL5xCZX;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getCreateTestUsersAndReviewsCommandService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Command\CreateTestUsersAndReviewsCommand' shared autowired service.
     *
     * @return \App\Command\CreateTestUsersAndReviewsCommand
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'console'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'Command.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Command'.\DIRECTORY_SEPARATOR.'CreateTestUsersAndReviewsCommand.php';

        $container->privates['App\\Command\\CreateTestUsersAndReviewsCommand'] = $instance = new \App\Command\CreateTestUsersAndReviewsCommand(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['security.user_password_hasher'] ?? $container->load('getSecurity_UserPasswordHasherService')));

        $instance->setName('app:create-test-users-reviews');
        $instance->setDescription('Create 3 test users and add certified reviews for CS101 course');

        return $instance;
    }
}
