<?php

namespace ContainerQL5xCZX;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getPromotionalBannerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.kleoBdA.App\Entity\PromotionalBanner' shared service.
     *
     * @return \App\Entity\PromotionalBanner
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.kleoBdA": it needs an instance of "App\\Entity\\PromotionalBanner" but this type has been excluded in "config/services.yaml".');
    }
}
