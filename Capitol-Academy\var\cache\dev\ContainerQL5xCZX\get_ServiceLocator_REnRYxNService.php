<?php

namespace ContainerQL5xCZX;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_REnRYxNService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.REnRYxN' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.REnRYxN'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'entityManager' => ['services', 'doctrine.orm.default_entity_manager', 'getDoctrine_Orm_DefaultEntityManagerService', false],
            'moduleRepository' => ['privates', 'App\\Repository\\OnsiteCourseModuleRepository', 'getOnsiteCourseModuleRepositoryService', true],
            'onsiteCourseRepository' => ['privates', 'App\\Repository\\OnsiteCourseRepository', 'getOnsiteCourseRepositoryService', true],
        ], [
            'entityManager' => '?',
            'moduleRepository' => 'App\\Repository\\OnsiteCourseModuleRepository',
            'onsiteCourseRepository' => 'App\\Repository\\OnsiteCourseRepository',
        ]);
    }
}
