<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/category/index.html.twig */
class __TwigTemplate_f88a676748ef3d9d7198619ca336b216 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/category/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/category/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Categories Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Categories Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Categories</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Categories Management", "page_icon" => "fas fa-folder", "search_placeholder" => "Search categories by name or description...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_category_new"), "text" => "Add New Category", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Categories", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 25
(isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 25, $this->source); })())), "icon" => "fas fa-layer-group", "color" => "#011a2d", "gradient" => "linear-gradient(135deg, #011a2d 0%, #1a3461 100%)"], ["title" => "Active", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 32
(isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 32, $this->source); })()), function ($__category__) use ($context, $macros) { $context["category"] = $__category__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 32, $this->source); })()), "active", [], "any", false, false, false, 32); })), "icon" => "fas fa-check-circle", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "For Courses", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 39
(isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 39, $this->source); })()), function ($__category__) use ($context, $macros) { $context["category"] = $__category__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 39, $this->source); })()), "displayInCourses", [], "any", false, false, false, 39); })), "icon" => "fas fa-graduation-cap", "color" => "#007bff", "gradient" => "linear-gradient(135deg, #007bff 0%, #0056b3 100%)"], ["title" => "For Videos", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 46, $this->source); })()), function ($__category__) use ($context, $macros) { $context["category"] = $__category__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["category"]) || array_key_exists("category", $context) ? $context["category"] : (function () { throw new RuntimeError('Variable "category" does not exist.', 46, $this->source); })()), "displayInVideos", [], "any", false, false, false, 46); })), "icon" => "fas fa-video", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/category/index.html.twig", 54, "740536809")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 103
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 104
        yield "<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.category-row',
        ['.category-name', '.category-description']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});





// Category management functions - Direct toggle without confirmation
function toggleCategoryInCourses(categoryId, categoryName, currentStatus) {
    executeCategoryCoursesToggle(categoryId);
}

function toggleCategoryInVideos(categoryId, categoryName, currentStatus) {
    executeCategoryVideosToggle(categoryId);
}

// Actual execution functions
function executeCategoryCoursesToggle(categoryId) {
    fetch(`/admin/categories/\${categoryId}/toggle-courses`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the category courses status.');
    });
}

function executeCategoryVideosToggle(categoryId) {
    fetch(`/admin/categories/\${categoryId}/toggle-videos`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the category videos status.'); // REMOVED
    });
}

function deleteCategoryConfirm(categoryId, categoryName) {
    AdminPageUtils.showDeleteModal(categoryId, categoryName, deleteCategory);
}

function deleteCategory(categoryId) {
    fetch(`/admin/categories/\${categoryId}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the category');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the category.');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/category/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  187 => 104,  174 => 103,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Categories Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Categories Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Categories</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Categories Management',
    'page_icon': 'fas fa-folder',
    'search_placeholder': 'Search categories by name or description...',
    'create_button': {
        'url': path('admin_category_new'),
        'text': 'Add New Category',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Categories',
            'value': categories|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': categories|filter(category => category.active)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'For Courses',
            'value': categories|filter(category => category.displayInCourses)|length,
            'icon': 'fas fa-graduation-cap',
            'color': '#007bff',
            'gradient': 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)'
        },
        {
            'title': 'For Videos',
            'value': categories|filter(category => category.displayInVideos)|length,
            'icon': 'fas fa-video',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Name'},
            {'text': 'Display Options'},
            {'text': 'Actions', 'style': 'width: 250px;'}
        ] %}

        {% set table_rows = [] %}
        {% for category in categories %}
            {% set row_cells = [
                {
                    'content': '<h6 class=\"category-name mb-0 font-weight-bold text-dark\">' ~ category.name ~ '</h6>'
                },
                {
                    'content': '<div class=\"d-flex gap-1\">
                        ' ~ (category.displayInCourses ? '<span class=\"badge\" style=\"background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\"><i class=\"fas fa-graduation-cap mr-1\"></i>Courses</span>' : '') ~ '
                        ' ~ (category.displayInVideos ? '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\"><i class=\"fas fa-video mr-1\"></i>Videos</span>' : '') ~ '
                        ' ~ (not category.displayInCourses and not category.displayInVideos ? '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\">None</span>' : '') ~ '
                    </div>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_category_show', {'id': category.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Category\"><i class=\"fas fa-eye\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (category.displayInCourses ? '#ffc107' : '#28a745') ~ '; color: ' ~ (category.displayInCourses ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (category.displayInCourses ? 'Deactivate' : 'Activate') ~ ' in Courses\" onclick=\"toggleCategoryInCourses(' ~ category.id ~ ', \\'' ~ category.name ~ '\\', ' ~ (category.displayInCourses ? 'true' : 'false') ~ ')\"><i class=\"fas fa-graduation-cap\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (category.displayInVideos ? '#ffc107' : '#28a745') ~ '; color: ' ~ (category.displayInVideos ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (category.displayInVideos ? 'Deactivate' : 'Activate') ~ ' in Videos\" onclick=\"toggleCategoryInVideos(' ~ category.id ~ ', \\'' ~ category.name ~ '\\', ' ~ (category.displayInVideos ? 'true' : 'false') ~ ')\"><i class=\"fas fa-video\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #dc3545; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Category\" onclick=\"deleteCategoryConfirm(' ~ category.id ~ ', \\'' ~ category.name ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'category-row',
            'empty_message': 'No categories found',
            'empty_icon': 'fas fa-folder',
            'empty_description': 'Get started by adding your first category.',
            'search_config': {
                'fields': ['.category-name', '.category-description']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.category-row',
        ['.category-name', '.category-description']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});





// Category management functions - Direct toggle without confirmation
function toggleCategoryInCourses(categoryId, categoryName, currentStatus) {
    executeCategoryCoursesToggle(categoryId);
}

function toggleCategoryInVideos(categoryId, categoryName, currentStatus) {
    executeCategoryVideosToggle(categoryId);
}

// Actual execution functions
function executeCategoryCoursesToggle(categoryId) {
    fetch(`/admin/categories/\${categoryId}/toggle-courses`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the category courses status.');
    });
}

function executeCategoryVideosToggle(categoryId) {
    fetch(`/admin/categories/\${categoryId}/toggle-videos`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the category videos status.'); // REMOVED
    });
}

function deleteCategoryConfirm(categoryId, categoryName) {
    AdminPageUtils.showDeleteModal(categoryId, categoryName, deleteCategory);
}

function deleteCategory(categoryId) {
    fetch(`/admin/categories/\${categoryId}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the category');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the category.');
    });
}
</script>
{% endblock %}
", "admin/category/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\category\\index.html.twig");
    }
}


/* admin/category/index.html.twig */
class __TwigTemplate_f88a676748ef3d9d7198619ca336b216___740536809 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/category/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/category/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "        <!-- Standardized Table -->
        ";
        // line 57
        $context["table_headers"] = [["text" => "Name"], ["text" => "Display Options"], ["text" => "Actions", "style" => "width: 250px;"]];
        // line 62
        yield "
        ";
        // line 63
        $context["table_rows"] = [];
        // line 64
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["categories"]) || array_key_exists("categories", $context) ? $context["categories"] : (function () { throw new RuntimeError('Variable "categories" does not exist.', 64, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["category"]) {
            // line 65
            yield "            ";
            $context["row_cells"] = [["content" => (("<h6 class=\"category-name mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 67
$context["category"], "name", [], "any", false, false, false, 67)) . "</h6>")], ["content" => (((((("<div class=\"d-flex gap-1\">
                        " . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 71
$context["category"], "displayInCourses", [], "any", false, false, false, 71)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\"><i class=\"fas fa-graduation-cap mr-1\"></i>Courses</span>") : (""))) . "
                        ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 72
$context["category"], "displayInVideos", [], "any", false, false, false, 72)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\"><i class=\"fas fa-video mr-1\"></i>Videos</span>") : (""))) . "
                        ") . ((( !CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["category"], "displayInCourses", [], "any", false, false, false, 73) &&  !CoreExtension::getAttribute($this->env, $this->source, $context["category"], "displayInVideos", [], "any", false, false, false, 73))) ? ("<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\">None</span>") : (""))) . "
                    </div>")], ["content" => (((((((((((((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_category_show", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["category"], "id", [], "any", false, false, false, 78)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Category\"><i class=\"fas fa-eye\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 79
$context["category"], "displayInCourses", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#ffc107") : ("#28a745"))) . "; color: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["category"], "displayInCourses", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#212529") : ("white"))) . "; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["category"], "displayInCourses", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " in Courses\" onclick=\"toggleCategoryInCourses(") . CoreExtension::getAttribute($this->env, $this->source, $context["category"], "id", [], "any", false, false, false, 79)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 79)) . "', ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["category"], "displayInCourses", [], "any", false, false, false, 79)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("true") : ("false"))) . ")\"><i class=\"fas fa-graduation-cap\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 80
$context["category"], "displayInVideos", [], "any", false, false, false, 80)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#ffc107") : ("#28a745"))) . "; color: ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["category"], "displayInVideos", [], "any", false, false, false, 80)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#212529") : ("white"))) . "; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["category"], "displayInVideos", [], "any", false, false, false, 80)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " in Videos\" onclick=\"toggleCategoryInVideos(") . CoreExtension::getAttribute($this->env, $this->source, $context["category"], "id", [], "any", false, false, false, 80)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 80)) . "', ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["category"], "displayInVideos", [], "any", false, false, false, 80)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("true") : ("false"))) . ")\"><i class=\"fas fa-video\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #dc3545; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Category\" onclick=\"deleteCategoryConfirm(") . CoreExtension::getAttribute($this->env, $this->source,             // line 81
$context["category"], "id", [], "any", false, false, false, 81)) . ", '") . CoreExtension::getAttribute($this->env, $this->source, $context["category"], "name", [], "any", false, false, false, 81)) . "')\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 85
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 85, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 85, $this->source); })())]]);
            // line 86
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['category'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 87
        yield "
        ";
        // line 88
        yield from $this->load("components/admin_table.html.twig", 88)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 89
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 89, $this->source); })()), "rows" =>         // line 90
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 90, $this->source); })()), "row_class" => "category-row", "empty_message" => "No categories found", "empty_icon" => "fas fa-folder", "empty_description" => "Get started by adding your first category.", "search_config" => ["fields" => [".category-name", ".category-description"]]]));
        // line 99
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/category/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  642 => 99,  640 => 90,  639 => 89,  638 => 88,  635 => 87,  629 => 86,  626 => 85,  623 => 81,  621 => 80,  619 => 79,  617 => 78,  614 => 73,  612 => 72,  610 => 71,  608 => 67,  606 => 65,  601 => 64,  599 => 63,  596 => 62,  594 => 57,  591 => 56,  578 => 55,  555 => 54,  187 => 104,  174 => 103,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Categories Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Categories Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Categories</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Categories Management',
    'page_icon': 'fas fa-folder',
    'search_placeholder': 'Search categories by name or description...',
    'create_button': {
        'url': path('admin_category_new'),
        'text': 'Add New Category',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Categories',
            'value': categories|length,
            'icon': 'fas fa-layer-group',
            'color': '#011a2d',
            'gradient': 'linear-gradient(135deg, #011a2d 0%, #1a3461 100%)'
        },
        {
            'title': 'Active',
            'value': categories|filter(category => category.active)|length,
            'icon': 'fas fa-check-circle',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'For Courses',
            'value': categories|filter(category => category.displayInCourses)|length,
            'icon': 'fas fa-graduation-cap',
            'color': '#007bff',
            'gradient': 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)'
        },
        {
            'title': 'For Videos',
            'value': categories|filter(category => category.displayInVideos)|length,
            'icon': 'fas fa-video',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}
        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Name'},
            {'text': 'Display Options'},
            {'text': 'Actions', 'style': 'width: 250px;'}
        ] %}

        {% set table_rows = [] %}
        {% for category in categories %}
            {% set row_cells = [
                {
                    'content': '<h6 class=\"category-name mb-0 font-weight-bold text-dark\">' ~ category.name ~ '</h6>'
                },
                {
                    'content': '<div class=\"d-flex gap-1\">
                        ' ~ (category.displayInCourses ? '<span class=\"badge\" style=\"background: #007bff; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\"><i class=\"fas fa-graduation-cap mr-1\"></i>Courses</span>' : '') ~ '
                        ' ~ (category.displayInVideos ? '<span class=\"badge\" style=\"background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\"><i class=\"fas fa-video mr-1\"></i>Videos</span>' : '') ~ '
                        ' ~ (not category.displayInCourses and not category.displayInVideos ? '<span class=\"badge\" style=\"background: #6c757d; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;\">None</span>' : '') ~ '
                    </div>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_category_show', {'id': category.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: #17a2b8; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Category\"><i class=\"fas fa-eye\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (category.displayInCourses ? '#ffc107' : '#28a745') ~ '; color: ' ~ (category.displayInCourses ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (category.displayInCourses ? 'Deactivate' : 'Activate') ~ ' in Courses\" onclick=\"toggleCategoryInCourses(' ~ category.id ~ ', \\'' ~ category.name ~ '\\', ' ~ (category.displayInCourses ? 'true' : 'false') ~ ')\"><i class=\"fas fa-graduation-cap\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: ' ~ (category.displayInVideos ? '#ffc107' : '#28a745') ~ '; color: ' ~ (category.displayInVideos ? '#212529' : 'white') ~ '; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (category.displayInVideos ? 'Deactivate' : 'Activate') ~ ' in Videos\" onclick=\"toggleCategoryInVideos(' ~ category.id ~ ', \\'' ~ category.name ~ '\\', ' ~ (category.displayInVideos ? 'true' : 'false') ~ ')\"><i class=\"fas fa-video\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: #dc3545; color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Category\" onclick=\"deleteCategoryConfirm(' ~ category.id ~ ', \\'' ~ category.name ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'category-row',
            'empty_message': 'No categories found',
            'empty_icon': 'fas fa-folder',
            'empty_description': 'Get started by adding your first category.',
            'search_config': {
                'fields': ['.category-name', '.category-description']
            }
        } %}
    {% endblock %}
{% endembed %}
{% endblock %}

{% block javascripts %}
<script>
\$(document).ready(function() {
    // Initialize standardized search
    AdminPageUtils.initializeSearch(
        '#professional-search',
        '.category-row',
        ['.category-name', '.category-description']
    );

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});





// Category management functions - Direct toggle without confirmation
function toggleCategoryInCourses(categoryId, categoryName, currentStatus) {
    executeCategoryCoursesToggle(categoryId);
}

function toggleCategoryInVideos(categoryId, categoryName, currentStatus) {
    executeCategoryVideosToggle(categoryId);
}

// Actual execution functions
function executeCategoryCoursesToggle(categoryId) {
    fetch(`/admin/categories/\${categoryId}/toggle-courses`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the category courses status.');
    });
}

function executeCategoryVideosToggle(categoryId) {
    fetch(`/admin/categories/\${categoryId}/toggle-videos`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            // alert(data.message || 'An error occurred'); // REMOVED
        }
    })
    .catch(error => {
        console.error('Error:', error);
        // alert('An error occurred while updating the category videos status.'); // REMOVED
    });
}

function deleteCategoryConfirm(categoryId, categoryName) {
    AdminPageUtils.showDeleteModal(categoryId, categoryName, deleteCategory);
}

function deleteCategory(categoryId) {
    fetch(`/admin/categories/\${categoryId}/delete`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred while deleting the category');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the category.');
    });
}
</script>
{% endblock %}
", "admin/category/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\category\\index.html.twig");
    }
}
