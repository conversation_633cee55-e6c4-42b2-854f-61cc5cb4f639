<?php

namespace ContainerGHTN61x;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getInstructorRepositoryService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Repository\InstructorRepository' shared autowired service.
     *
     * @return \App\Repository\InstructorRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Repository'.\DIRECTORY_SEPARATOR.'InstructorRepository.php';

        return $container->privates['App\\Repository\\InstructorRepository'] = new \App\Repository\InstructorRepository(($container->services['doctrine'] ?? self::getDoctrineService($container)));
    }
}
