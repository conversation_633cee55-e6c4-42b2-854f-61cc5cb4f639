{% extends 'base.html.twig' %}

{% block title %}Best Technical Analysis Strategies - Capitol Academy{% endblock %}

{% block meta_description %}Master the best technical analysis strategies with Capitol Academy's comprehensive video courses. Learn professional trading techniques from industry experts.{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Professional Course Catalog Styles */
        .hero-section {
            background: url('{{ asset('images/backgrounds/Bg Best Technical Analysis Strategies.png') }}') center/cover no-repeat;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            padding: 120px 0 80px;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            color: white;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 0;
            font-family: 'Montserrat', sans-serif;
            line-height: 1.2;
        }

        .hero-title .best {
            color: white;
            font-weight: 400;
        }

        .hero-title .technical-analysis {
            color: #87CEEB;
            font-weight: 400;
        }

        .hero-title .strategies {
            color: white;
            font-weight: 700;
        }

        /* Course Cards Section */
        .course-cards-section {
            background: white;
            padding: 80px 0;
        }

        .course-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .course-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            border: 1px solid #f1f3f4;
        }

        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .course-icon {
            width: 50px;
            height: 50px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            color: #011a2d;
            font-size: 1.5rem;
        }

        .course-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.4rem;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .course-description {
            color: #6c757d;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
            font-family: 'Calibri', Arial, sans-serif;
        }

        .learn-more-btn {
            background: #011a2d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            font-size: 0.95rem;
        }

        .learn-more-btn:hover {
            background: #1a3461;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }

        .learn-more-btn.disabled {
            background: #6c757d;
            cursor: not-allowed;
            pointer-events: none;
        }

        .learn-more-btn.disabled:hover {
            background: #6c757d;
            transform: none;
        }

        .load-more-section {
            text-align: center;
            margin-top: 50px;
        }

        .load-more-btn {
            background: white;
            color: #011a2d;
            border: 2px solid #011a2d;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            font-size: 1rem;
        }

        .load-more-btn:hover {
            background: #011a2d;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }

        /* Contact Form Section */
        .contact-section {
            background: url('{{ asset('images/backgrounds/Bg Want to Become a Pro Trader.png') }}') center/cover no-repeat;
            padding: 80px 0;
            position: relative;
        }

        .contact-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.4);
            z-index: 1;
        }

        .contact-content {
            position: relative;
            z-index: 2;
        }

        .contact-text {
            color: white;
        }

        .contact-title {
            font-family: 'Montserrat', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 30px;
            line-height: 1.2;
        }

        .contact-title .pro-trader {
            background: linear-gradient(135deg, #28a745, #20c997);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .contact-features {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .contact-features li {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
            font-family: 'Calibri', Arial, sans-serif;
        }

        .contact-features li i {
            color: #28a745;
            margin-right: 15px;
            font-size: 1.2rem;
        }

        .contact-form-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-form .form-group {
            margin-bottom: 20px;
        }

        .contact-form .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .contact-form .form-control:focus {
            background: white;
            border-color: #011a2d;
            box-shadow: 0 0 0 3px rgba(1, 26, 45, 0.1);
            outline: none;
        }

        .contact-form textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .terms-checkbox {
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
        }

        .terms-checkbox input[type="checkbox"] {
            margin-right: 10px;
            margin-top: 3px;
        }

        .terms-checkbox label {
            color: white;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .submit-btn {
            background: #a90418;
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .submit-btn:hover {
            background: #8b0314;
            transform: translateY(-2px);
        }

        /* All Videos Section */
        .all-videos-section {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .video-card-compact {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .video-card-compact:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .video-thumbnail-compact {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .video-thumbnail-compact img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .placeholder-thumbnail {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 2rem;
        }

        .video-badge-compact {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .video-content-compact {
            padding: 20px;
        }

        .video-title-compact {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 10px;
            font-family: 'Montserrat', sans-serif;
            font-size: 1.1rem;
        }

        .video-description-compact {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .course-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .contact-title {
                font-size: 2rem;
            }

            .contact-form-container {
                padding: 25px;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }

            .course-card {
                padding: 20px;
            }

            .course-title {
                font-size: 1.2rem;
            }
        }
    </style>
{% endblock %}

{% block body %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="best">Best</span><br>
                        <span class="technical-analysis">Technical Analysis</span><br>
                        <span class="strategies">Strategies</span>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Course Cards Section -->
<section class="course-cards-section">
    <div class="container">
        <div class="course-grid">
            {% set course_data = [
                {
                    'icon': 'fas fa-chart-line',
                    'title': 'How To Trade the Doji Candle?',
                    'description': 'This is an excellent course for those wishing to expand their knowledge of how priced.',
                    'slug': videos[0] is defined ? videos[0].slug : '#',
                    'video': videos[0] ?? null
                },
                {
                    'icon': 'fas fa-trending-up',
                    'title': 'What Are Support and Resistance Levels?',
                    'description': 'This is an excellent course for those wishing to expand their knowledge of how priced.',
                    'slug': videos[1] is defined ? videos[1].slug : '#',
                    'video': videos[1] ?? null
                },
                {
                    'icon': 'fas fa-chart-bar',
                    'title': 'How to Use Moving Averages?',
                    'description': 'This is an excellent course for those wishing to expand their knowledge of how priced.',
                    'slug': videos[2] is defined ? videos[2].slug : '#',
                    'video': videos[2] ?? null
                },
                {
                    'icon': 'fas fa-signal',
                    'title': 'Understanding RSI Indicators?',
                    'description': 'This is an excellent course for those wishing to expand their knowledge of how priced.',
                    'slug': videos[3] is defined ? videos[3].slug : '#',
                    'video': videos[3] ?? null
                },
                {
                    'icon': 'fas fa-exchange-alt',
                    'title': 'How to Read Forex Charts?',
                    'description': 'This is an excellent course for those wishing to expand their knowledge of how priced.',
                    'slug': videos[4] is defined ? videos[4].slug : '#',
                    'video': videos[4] ?? null
                },
                {
                    'icon': 'fas fa-coins',
                    'title': 'Risk Management Strategies?',
                    'description': 'This is an excellent course for those wishing to expand their knowledge of how priced.',
                    'slug': videos[5] is defined ? videos[5].slug : '#',
                    'video': videos[5] ?? null
                }
            ] %}

            {% for course in course_data %}
                <div class="course-card">
                    <div class="course-icon">
                        <i class="{{ course.icon }}"></i>
                    </div>
                    <h3 class="course-title">
                        {% if course.video %}
                            {{ course.video.title }}
                        {% else %}
                            {{ course.title }}
                        {% endif %}
                    </h3>
                    <p class="course-description">
                        {% if course.video and course.video.description %}
                            {{ course.video.description|length > 120 ? course.video.description|slice(0, 120) ~ '...' : course.video.description }}
                        {% else %}
                            {{ course.description }}
                        {% endif %}
                    </p>
                    <a href="{{ course.slug != '#' ? path('app_video_show', {'slug': course.slug}) : '#' }}"
                       class="learn-more-btn {{ course.slug == '#' ? 'disabled' : '' }}">
                        {% if course.slug != '#' %}
                            Learn More
                        {% else %}
                            Coming Soon
                        {% endif %}
                    </a>
                </div>
            {% endfor %}
        </div>

        <div class="load-more-section">
            <a href="{{ path('app_videos') }}#all-videos" class="load-more-btn">Load More Trading Strategies</a>
        </div>
    </div>
</section>
<!-- Contact Form Section -->
<section class="contact-section">
    <div class="container">
        <div class="contact-content">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="contact-text">
                        <h2 class="contact-title">
                            Want to Become a <span class="pro-trader">Pro Trader</span>?<br>
                            Contact us
                        </h2>
                        <ul class="contact-features">
                            <li>
                                <i class="fas fa-check-circle"></i>
                                Expert guidance from professional traders
                            </li>
                            <li>
                                <i class="fas fa-check-circle"></i>
                                Comprehensive technical analysis training
                            </li>
                            <li>
                                <i class="fas fa-check-circle"></i>
                                Real-time market insights and strategies
                            </li>
                            <li>
                                <i class="fas fa-check-circle"></i>
                                Personalized learning experience
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-form-container">
                        <form action="{{ path('app_contact_unified') }}" method="POST" class="contact-form">
                            <input type="hidden" name="source_page" value="video_landing">

                            <div class="form-group">
                                <input type="text" name="name" class="form-control" placeholder="Your Name" required>
                            </div>

                            <div class="form-group">
                                <input type="email" name="email" class="form-control" placeholder="Your Email" required>
                            </div>

                            <div class="form-group">
                                <textarea name="message" class="form-control" placeholder="Your Message" rows="4" required></textarea>
                            </div>

                            <div class="terms-checkbox">
                                <input type="checkbox" id="terms" name="terms" required>
                                <label for="terms">
                                    I agree to the <a href="#" style="color: #87CEEB;">Terms and Conditions</a> and
                                    <a href="#" style="color: #87CEEB;">Privacy Policy</a>
                                </label>
                            </div>

                            <button type="submit" class="submit-btn">
                                Submit
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- All Videos Section (Hidden by default, shown when Load More is clicked) -->
<section class="all-videos-section" id="all-videos" style="display: none;">
    <div class="container">
        <div class="section-title text-center mb-5">
            <h2 style="color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;">All Trading Strategies</h2>
            <p style="color: #6c757d;">Explore our complete collection of professional trading education videos</p>
        </div>

        <div class="row">
            {% if videos|length > 0 %}
                {% for video in videos %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="video-card-compact">
                            <div class="video-thumbnail-compact">
                                {% if video.thumbnail %}
                                    <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}"
                                         alt="{{ video.title }}" loading="lazy">
                                {% else %}
                                    <div class="placeholder-thumbnail">
                                        <i class="fas fa-video"></i>
                                    </div>
                                {% endif %}

                                <!-- Access Level Badge -->
                                <div class="video-badge-compact">
                                    {% if video.accessLevel == 'public_free' %}
                                        <span class="badge badge-success">Free</span>
                                    {% elseif video.accessLevel == 'login_required_free' %}
                                        <span class="badge badge-info">Login Required</span>
                                    {% else %}
                                        <span class="badge badge-warning">{{ video.formattedPrice }}</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="video-content-compact">
                                <h5 class="video-title-compact">{{ video.title }}</h5>
                                {% if video.description %}
                                    <p class="video-description-compact">
                                        {{ video.description|length > 80 ? video.description|slice(0, 80) ~ '...' : video.description }}
                                    </p>
                                {% endif %}
                                <a href="{{ path('app_video_show', {'slug': video.slug}) }}" class="btn btn-primary btn-sm">
                                    Watch Now
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="col-12 text-center">
                    <p style="color: #6c757d; font-size: 1.1rem;">No videos available at the moment. Please check back soon!</p>
                </div>
            {% endif %}
        </div>
    </div>
</section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Form validation and submission
            const contactForm = document.querySelector('.contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    const termsCheckbox = this.querySelector('#terms');
                    if (!termsCheckbox.checked) {
                        e.preventDefault();
                        alert('Please accept the Terms and Conditions to continue.');
                        return false;
                    }
                });
            }

            // Load more button functionality
            const loadMoreBtn = document.querySelector('.load-more-btn');
            const allVideosSection = document.getElementById('all-videos');

            if (loadMoreBtn && allVideosSection) {
                loadMoreBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Show the all videos section
                    allVideosSection.style.display = 'block';

                    // Smooth scroll to the section
                    allVideosSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update button text
                    this.textContent = 'View All Strategies';
                    this.style.background = '#28a745';
                    this.style.borderColor = '#28a745';
                });
            }

            // Handle URL hash for direct access to all videos
            if (window.location.hash === '#all-videos' && allVideosSection) {
                allVideosSection.style.display = 'block';
                if (loadMoreBtn) {
                    loadMoreBtn.textContent = 'View All Strategies';
                    loadMoreBtn.style.background = '#28a745';
                    loadMoreBtn.style.borderColor = '#28a745';
                }
            }

            // Course card hover effects
            const courseCards = document.querySelectorAll('.course-card');
            courseCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Animate elements on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe course cards for animation
            courseCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
{% endblock %}