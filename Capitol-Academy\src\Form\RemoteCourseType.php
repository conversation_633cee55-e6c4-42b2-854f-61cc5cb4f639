<?php

namespace App\Form;

use App\Entity\RemoteCourse;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Positive;

class RemoteCourseType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('code', TextType::class, [
                'label' => 'Course Code',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'e.g., RC001',
                    'maxlength' => 10
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Course code is required'])
                ],
                'help' => 'Unique identifier for the remote course (max 10 characters)'
            ])
            ->add('title', TextType::class, [
                'label' => 'Course Title',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Enter course title',
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Course title is required'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Course Description',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'rows' => 5,
                    'placeholder' => 'Detailed description of the course content and objectives'
                ]
            ])
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => [
                    'Trading Fundamentals' => 'Trading Fundamentals',
                    'Technical Analysis' => 'Technical Analysis',
                    'Risk Management' => 'Risk Management',
                    'Market Psychology' => 'Market Psychology',
                    'Advanced Strategies' => 'Advanced Strategies',
                    'Cryptocurrency' => 'Cryptocurrency',
                    'Forex Trading' => 'Forex Trading',
                    'Stock Market' => 'Stock Market',
                    'Options Trading' => 'Options Trading',
                    'Algorithmic Trading' => 'Algorithmic Trading'
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'constraints' => [
                    new NotBlank(['message' => 'Category is required'])
                ]
            ])
            ->add('level', ChoiceType::class, [
                'label' => 'Difficulty Level',
                'required' => false,
                'choices' => [
                    'Beginner' => 'Beginner',
                    'Intermediate' => 'Intermediate',
                    'Advanced' => 'Advanced',
                    'Expert' => 'Expert'
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'placeholder' => 'Select difficulty level'
            ])
            ->add('instructor', TextType::class, [
                'label' => 'Instructor',
                'required' => false,
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => 'Instructor name',
                    'maxlength' => 255
                ]
            ])
            ->add('price', MoneyType::class, [
                'label' => 'Course Price',
                'currency' => 'USD',
                'attr' => [
                    'class' => 'form-control',
                    'placeholder' => '0.00'
                ],
                'constraints' => [
                    new Positive(['message' => 'Price must be positive'])
                ]
            ])
            ->add('thumbnail_image', FileType::class, [
                'label' => 'Thumbnail Image',
                'required' => false,
                'mapped' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '2M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, WebP)'
                    ])
                ],
                'help' => 'Recommended size: 300x200px (max 2MB)'
            ])
            ->add('banner_image', FileType::class, [
                'label' => 'Banner Image',
                'required' => false,
                'mapped' => false,
                'attr' => [
                    'class' => 'form-control',
                    'accept' => 'image/*'
                ],
                'constraints' => [
                    new File([
                        'maxSize' => '5M',
                        'mimeTypes' => [
                            'image/jpeg',
                            'image/png',
                            'image/webp'
                        ],
                        'mimeTypesMessage' => 'Please upload a valid image file (JPEG, PNG, WebP)'
                    ])
                ],
                'help' => 'Recommended size: 1200x400px (max 5MB)'
            ])
            ->add('learning_outcomes', CollectionType::class, [
                'label' => 'Learning Outcomes',
                'entry_type' => TextType::class,
                'entry_options' => [
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'What will students learn?'
                    ]
                ],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
                'attr' => [
                    'class' => 'learning-outcomes-collection'
                ],
                'help' => 'Add specific learning outcomes for this course'
            ])
            ->add('features', CollectionType::class, [
                'label' => 'Course Features',
                'entry_type' => TextType::class,
                'entry_options' => [
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'Course feature or benefit'
                    ]
                ],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
                'attr' => [
                    'class' => 'features-collection'
                ],
                'help' => 'Add key features and benefits of this course'
            ])
            ->add('tags', CollectionType::class, [
                'label' => 'Tags',
                'entry_type' => TextType::class,
                'entry_options' => [
                    'attr' => [
                        'class' => 'form-control',
                        'placeholder' => 'Tag'
                    ]
                ],
                'allow_add' => true,
                'allow_delete' => true,
                'by_reference' => false,
                'required' => false,
                'attr' => [
                    'class' => 'tags-collection'
                ],
                'help' => 'Add tags to help categorize and search for this course'
            ])
            ->add('is_active', ChoiceType::class, [
                'label' => 'Status',
                'choices' => [
                    'Active' => true,
                    'Inactive' => false
                ],
                'attr' => [
                    'class' => 'form-select'
                ],
                'help' => 'Only active courses are visible to students'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => RemoteCourse::class,
            'attr' => [
                'class' => 'remote-course-form',
                'novalidate' => 'novalidate'
            ]
        ]);
    }
}
