<?php

namespace ContainerGHTN61x;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_KHM8GDbService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.KHM8GDb' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.KHM8GDb'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', '.errored..Eoabet', NULL, 'Cannot determine controller argument for "App\\Controller\\HomeController::about()": the $courseRepository argument is type-hinted with the non-existent class or interface: "App\\Repository\\CourseRepository".'],
        ], [
            'courseRepository' => '?',
        ]);
    }
}
