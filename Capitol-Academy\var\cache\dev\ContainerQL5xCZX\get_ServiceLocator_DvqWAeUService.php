<?php

namespace ContainerQL5xCZX;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_DvqWAeUService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.DvqWAeU' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.DvqWAeU'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'userVideoAccessRepository' => ['privates', 'App\\Repository\\UserVideoAccessRepository', 'getUserVideoAccessRepositoryService', true],
        ], [
            'userVideoAccessRepository' => 'App\\Repository\\UserVideoAccessRepository',
        ]);
    }
}
