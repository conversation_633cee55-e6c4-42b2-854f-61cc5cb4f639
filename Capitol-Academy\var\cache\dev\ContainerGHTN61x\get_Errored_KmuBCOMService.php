<?php

namespace ContainerGHTN61x;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_Errored_KmuBCOMService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored.kmuBCOM' shared service.
     *
     * @return \App\Controller\CourseRepository
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot determine controller argument for "App\\Controller\\CourseController::showModule()": the $courseRepository argument is type-hinted with the non-existent class or interface: "App\\Controller\\CourseRepository". Did you forget to add a use statement?');
    }
}
