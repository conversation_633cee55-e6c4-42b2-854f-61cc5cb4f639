a:2:{i:0;a:6:{s:4:"type";i:16384;s:7:"message";s:201:"Since doctrine/doctrine-bundle 2.12: The default value of "doctrine.orm.controller_resolver.auto_mapping" will be changed from `true` to `false`. Explicitly configure `true` to keep existing behaviour.";s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:504;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:122;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}i:1;a:6:{s:4:"type";i:16384;s:7:"message";s:173:"Since doctrine/doctrine-bundle 2.13: Enabling the controller resolver automapping feature has been deprecated. Symfony Mapped Route Parameters should be used as replacement.";s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:509;s:5:"trace";a:1:{i:0;a:5:{s:4:"file";s:132:"C:\Users\<USER>\Desktop\Capitol Academy\Capitol-Academy\vendor\doctrine\doctrine-bundle\src\DependencyInjection\DoctrineExtension.php";s:4:"line";i:122;s:8:"function";s:7:"ormLoad";s:5:"class";s:68:"Doctrine\Bundle\DoctrineBundle\DependencyInjection\DoctrineExtension";s:4:"type";s:2:"->";}}s:5:"count";i:1;}}