<?php

namespace ContainerQL5xCZX;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_YuEbyXLService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.YuEbyXL' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.YuEbyXL'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'partner' => ['privates', '.errored..service_locator.YuEbyXL.App\\Entity\\Partner', NULL, 'Cannot autowire service ".service_locator.YuEbyXL": it needs an instance of "App\\Entity\\Partner" but this type has been excluded in "config/services.yaml".'],
        ], [
            'partner' => 'App\\Entity\\Partner',
        ]);
    }
}
